/**
 * Products Management System
 * Handles product display, filtering, and search functionality
 */

class ProductManager {
    constructor() {
        this.products = this.getProducts();
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.init();
    }

    init() {
        this.renderProducts();
        this.initEventListeners();
        this.initSearch();
    }

    getProducts() {
        // Default products data - in a real app, this would come from an API
        return [
            {
                id: '1',
                name: 'جوزية بالكراميل',
                description: 'قطع جوزية محشية كراميل غني ومغطاة بشوكولاتة فاخرة',
                price: 7000,
                category: 'باقلاوة',
                image: 'https://readdy.ai/api/search-image?query=Luxurious%20Arabic%20baklava%20dessert%20with%20caramel%20filling%2C%20covered%20in%20premium%20chocolate.%20The%20dessert%20is%20photographed%20from%20above%20on%20a%20clean%20white%20plate%20with%20golden%20decorative%20elements.%20The%20lighting%20is%20bright%20and%20highlights%20the%20glossy%20chocolate%20coating%20and%20the%20caramel%20drizzle.%20The%20background%20is%20minimal%20and%20elegant%2C%20focusing%20on%20the%20exquisite%20details%20of%20the%20dessert.&width=600&height=400&seq=123457&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.8,
                reviews: 24
            },
            {
                id: '2',
                name: 'مادلين كيك بالبندق',
                description: 'كيك ناعم مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
                price: 4500,
                category: 'كيك',
                image: 'https://readdy.ai/api/search-image?query=Elegant%20Madeleine%20cakes%20covered%20in%20premium%20chocolate%20and%20garnished%20with%20roasted%20hazelnuts.%20The%20cakes%20are%20arranged%20on%20a%20golden%20serving%20tray%20with%20decorative%20elements.%20The%20lighting%20is%20soft%20and%20highlights%20the%20glossy%20chocolate%20coating%20and%20the%20texture%20of%20the%20hazelnuts.%20The%20background%20is%20clean%20and%20minimal%2C%20focusing%20on%20the%20delicate%20details%20of%20the%20dessert.%20The%20composition%20is%20professional%20and%20appetizing.&width=600&height=400&seq=123458&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.6,
                reviews: 18
            },
            {
                id: '3',
                name: 'حلى ڤيلا بالفول السوداني',
                description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
                price: 6000,
                category: 'حلويات',
                image: 'https://readdy.ai/api/search-image?query=Luxurious%20layered%20Arabic%20dessert%20with%20multiple%20cream%20layers%20and%20crunchy%20peanuts.%20The%20dessert%20is%20presented%20on%20an%20elegant%20golden%20plate%20with%20decorative%20elements.%20The%20lighting%20is%20bright%20and%20highlights%20the%20different%20textures%20and%20layers%20of%20the%20dessert.%20The%20background%20is%20clean%20and%20minimal%2C%20focusing%20on%20the%20exquisite%20details%20of%20the%20dessert.%20The%20composition%20is%20professional%20and%20appetizing.&width=600&height=400&seq=123459&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.9,
                reviews: 31
            }
        ];
    }

    initEventListeners() {
        // Category filter buttons (if they exist)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('category-filter')) {
                e.preventDefault();
                this.filterByCategory(e.target.dataset.category);
            }
        });

        // Product quick view (if implemented)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-view')) {
                e.preventDefault();
                this.showQuickView(e.target.dataset.productId);
            }
        });
    }

    initSearch() {
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value.toLowerCase();
                this.renderProducts();
            });
        }
    }

    filterByCategory(category) {
        this.currentCategory = category;
        this.renderProducts();
        
        // Update active category button
        document.querySelectorAll('.category-filter').forEach(btn => {
            btn.classList.remove('active', 'text-primary', 'border-primary');
            btn.classList.add('text-gray-600');
        });
        
        const activeBtn = document.querySelector(`[data-category="${category}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active', 'text-primary', 'border-primary');
            activeBtn.classList.remove('text-gray-600');
        }
    }

    getFilteredProducts() {
        let filtered = this.products;

        // Filter by category
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(product => 
                product.category === this.currentCategory
            );
        }

        // Filter by search query
        if (this.searchQuery) {
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(this.searchQuery) ||
                product.description.toLowerCase().includes(this.searchQuery) ||
                product.category.toLowerCase().includes(this.searchQuery)
            );
        }

        // Filter by availability
        filtered = filtered.filter(product => product.available);

        return filtered;
    }

    renderProducts() {
        const container = document.querySelector('#products .grid');
        if (!container) return;

        const filteredProducts = this.getFilteredProducts();

        if (filteredProducts.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <i class="ri-search-line ri-3x text-gray-400 dark:text-gray-500 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 dark:text-gray-300 mb-2">لا توجد منتجات</h3>
                    <p class="text-gray-500 dark:text-gray-400">لم نجد أي منتجات تطابق البحث الحالي</p>
                </div>
            `;
            return;
        }

        container.innerHTML = filteredProducts.map(product => 
            this.createProductCard(product)
        ).join('');
    }

    createProductCard(product) {
        const isOnSale = product.originalPrice && product.originalPrice > product.price;
        const discountPercent = isOnSale ? 
            Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden product-card hover:shadow-lg transition-all duration-300" data-product-id="${product.id}">
                <div class="relative h-64 overflow-hidden group">
                    <img src="${product.image}"
                         alt="${product.name}"
                         class="w-full h-full object-cover object-top group-hover:scale-105 transition-transform duration-300"
                         loading="lazy">

                    ${product.featured ? `
                        <div class="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-full text-xs font-bold">
                            مميز
                        </div>
                    ` : ''}

                    ${isOnSale ? `
                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            خصم ${discountPercent}%
                        </div>
                    ` : ''}

                    <!-- Favorite Button -->
                    <button class="favorite-btn absolute ${isOnSale ? 'top-14' : 'top-2'} left-2 w-10 h-10 bg-white dark:bg-gray-700 rounded-full shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center group z-10"
                            data-product-id="${product.id}"
                            aria-label="إضافة للمفضلة"
                            type="button">
                        <i class="ri-heart-line text-gray-600 dark:text-gray-300 group-hover:text-red-500 transition-colors duration-300"></i>
                    </button>

                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <button class="quick-view opacity-0 group-hover:opacity-100 bg-white dark:bg-gray-700 text-primary dark:text-primary px-4 py-2 rounded-full font-medium transition-all duration-300 transform translate-y-2 group-hover:translate-y-0"
                                data-product-id="${product.id}">
                            عرض سريع
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <div class="flex items-center mb-2">
                        ${this.createStarsRating(product.rating)}
                        <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">(${product.reviews})</span>
                    </div>

                    <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2 hover:text-primary transition-colors cursor-pointer">
                        ${product.name}
                    </h3>

                    <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">${product.description}</p>
                    
                    <div class="flex justify-between items-center">
                        <div class="flex flex-col">
                            <span class="text-primary font-bold text-lg">${product.price.toLocaleString()} د.ع</span>
                            ${isOnSale ? `
                                <span class="text-gray-400 dark:text-gray-500 line-through text-sm">${product.originalPrice.toLocaleString()} د.ع</span>
                            ` : ''}
                        </div>

                        <button class="add-to-cart bg-primary text-white px-4 py-2 rounded-button hover:bg-opacity-90 transition-all duration-300 hover:scale-105 font-medium"
                                data-id="${product.id}"
                                data-name="${product.name}"
                                data-price="${product.price}"
                                data-image="${product.image}">
                            <i class="ri-shopping-cart-line ml-1"></i>
                            إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    createStarsRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHTML = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="ri-star-fill text-yellow-400"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            starsHTML += '<i class="ri-star-half-fill text-yellow-400"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="ri-star-line text-gray-300 dark:text-gray-600"></i>';
        }

        return `<div class="flex items-center text-sm">${starsHTML}</div>`;
    }

    showQuickView(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // Create quick view modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-50 flex items-center justify-center p-4 transition-colors duration-300';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto transition-colors duration-300">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">${product.name}</h3>
                        <button class="close-quick-view text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-2xl transition-colors">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <img src="${product.image}" alt="${product.name}" class="w-full h-64 object-cover rounded-lg">
                        </div>
                        
                        <div>
                            <div class="flex items-center mb-3">
                                ${this.createStarsRating(product.rating)}
                                <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">(${product.reviews} تقييم)</span>
                            </div>

                            <p class="text-gray-600 dark:text-gray-300 mb-4">${product.description}</p>

                            <div class="mb-4">
                                <span class="text-primary font-bold text-2xl">${product.price.toLocaleString()} د.ع</span>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <i class="ri-checkbox-circle-line text-green-500 ml-2"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-300">مكونات طبيعية 100%</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="ri-truck-line text-blue-500 ml-2"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-300">توصيل مجاني داخل البصرة</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="ri-shield-check-line text-purple-500 ml-2"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-300">ضمان الجودة</span>
                                </div>
                            </div>

                            <button class="add-to-cart w-full mt-6 bg-primary text-white px-6 py-3 rounded-button hover:bg-opacity-90 transition-all duration-300 font-medium"
                                    data-id="${product.id}"
                                    data-name="${product.name}"
                                    data-price="${product.price}"
                                    data-image="${product.image}">
                                <i class="ri-shopping-cart-line ml-2"></i>
                                إضافة إلى السلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        // Close modal event
        modal.querySelector('.close-quick-view').addEventListener('click', () => {
            modal.remove();
            document.body.style.overflow = 'auto';
        });

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
            }
        });
    }

    // Method to add new product (for admin use)
    addProduct(productData) {
        const newProduct = {
            id: Date.now().toString(),
            ...productData,
            available: true,
            rating: 0,
            reviews: 0
        };
        
        this.products.push(newProduct);
        this.saveProducts();
        this.renderProducts();
    }

    // Method to update product
    updateProduct(productId, updates) {
        const index = this.products.findIndex(p => p.id === productId);
        if (index !== -1) {
            this.products[index] = { ...this.products[index], ...updates };
            this.saveProducts();
            this.renderProducts();
        }
    }

    // Method to delete product
    deleteProduct(productId) {
        this.products = this.products.filter(p => p.id !== productId);
        this.saveProducts();
        this.renderProducts();
    }

    saveProducts() {
        localStorage.setItem('products', JSON.stringify(this.products));
    }

    loadProducts() {
        const saved = localStorage.getItem('products');
        if (saved) {
            this.products = JSON.parse(saved);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.productManager = new ProductManager();
}); 
# 🍰 ڤيلا سويتز - متجر الحلويات العربية الفاخرة

## 📋 نظرة عامة

**ڤيلا سويتز (VelaSweets)** هو متجر إلكتروني متخصص في بيع الحلويات العربية الفاخرة. يهدف المشروع إلى تقديم تجربة تسوق سهلة وممتعة للعملاء مع واجهة باللغة العربية ودعم كامل للأجهزة المحمولة.

## ✨ المميزات الرئيسية

### 🎨 التصميم وتجربة المستخدم
- ✅ تصميم متجاوب يدعم جميع الأجهزة (Desktop, Tablet, Mobile)
- ✅ واجهة باللغة العربية مع دعم RTL
- ✅ دعم المظهر المظلم والمضيء
- ✅ تأثيرات حركية ناعمة وجذابة
- ✅ تصميم عصري باستخدام Tailwind CSS

### 🛒 وظائف التسوق
- ✅ سلة تسوق تفاعلية مع حفظ البيانات محلياً
- ✅ إضافة/إزالة/تعديل كمية المنتجات
- ✅ حساب المجموع تلقائياً
- ✅ نموذج إتمام الطلب المتكامل
- ✅ دعم طرق دفع متعددة

### 🌐 الوظائف العامة
- ✅ تمرير ناعم بين الأقسام
- ✅ قائمة للهواتف المحمولة
- ✅ نموذج الاتصال التفاعلي
- ✅ تبديل اللغات (العربية، الإنجليزية، الكردية)
- ✅ تبديل بين المظهر المضيء والمظلم

### 📱 تحسينات الأداء
- ✅ تحميل سريع للصفحات
- ✅ تحسينات SEO كاملة
- ✅ Open Graph Meta Tags
- ✅ صور محسنة للأداء
- ✅ كود منظم ومقسم لملفات منفصلة

## 🏗️ بنية المشروع

```
Velasweets/
├── index.html              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css       # التنسيقات المخصصة
│   └── js/
│       ├── main.js         # الوظائف العامة
│       ├── cart.js         # إدارة سلة التسوق
│       ├── checkout.js     # معالجة الطلبات
│       └── products.js     # إدارة المنتجات
├── README.md               # هذا الملف
└── .gitignore             # ملف استبعاد Git
```

## 📦 المتطلبات التقنية

### المكتبات المستخدمة
- **Tailwind CSS 3.4.16** - إطار عمل CSS
- **RemixIcon 4.6.0** - مكتبة الأيقونات
- **Google Fonts** - خطوط Cairo و Pacifico
- **Vanilla JavaScript** - لا يتطلب إطار عمل JavaScript

### المتصفحات المدعومة
- Chrome (آخر إصدارين)
- Firefox (آخر إصدارين)
- Safari (آخر إصدارين)
- Edge (آخر إصدارين)

## 🚀 التشغيل والتطوير

### التشغيل المحلي
1. استنسخ المشروع:
```bash
git clone https://github.com/username/velasweets.git
cd velasweets
```

2. افتح `index.html` في المتصفح:
```bash
# افتح الملف مباشرة أو استخدم خادم محلي
python -m http.server 8000
# أو
npx serve .
```

3. انتقل إلى `http://localhost:8000`

### التطوير
المشروع يستخدم ملفات HTML/CSS/JS بسيطة ولا يحتاج إلى عملية بناء معقدة.

## 📊 بنية البيانات

### المنتجات
```javascript
{
    id: '1',
    name: 'جوزية بالكراميل',
    description: 'قطع جوزية محشية كراميل غني ومغطاة بشوكولاتة فاخرة',
    price: 7000,
    category: 'باقلاوة',
    image: 'url',
    featured: true,
    available: true,
    rating: 4.8,
    reviews: 24
}
```

### سلة التسوق
```javascript
{
    id: '1',
    name: 'اسم المنتج',
    price: 7000,
    image: 'url',
    quantity: 2
}
```

### الطلبات
```javascript
{
    id: 'ORDER-123456',
    customerName: 'اسم العميل',
    customerPhone: '07901234567',
    customerProvince: 'البصرة',
    customerAddress: 'العنوان التفصيلي',
    paymentMethod: 'cash',
    cart: [...],
    totalAmount: 14000,
    totalItems: 2,
    status: 'pending',
    createdAt: '2025-01-01T12:00:00Z'
}
```

## 🎯 الوظائف التفصيلية

### 1. إدارة سلة التسوق (`cart.js`)
- **CartManager Class**: إدارة شاملة لسلة التسوق
- **addToCart()**: إضافة منتجات للسلة
- **updateQuantity()**: تحديث الكميات
- **removeFromCart()**: إزالة المنتجات
- **saveCart()**: حفظ في localStorage

### 2. معالجة الطلبات (`checkout.js`)
- **CheckoutManager Class**: إدارة عملية الشراء
- **validateField()**: التحقق من صحة البيانات
- **processOrder()**: معالجة الطلب
- **saveOrder()**: حفظ الطلب محلياً

### 3. إدارة المنتجات (`products.js`)
- **ProductManager Class**: عرض وإدارة المنتجات
- **renderProducts()**: عرض المنتجات ديناميكياً
- **showQuickView()**: عرض سريع للمنتج
- **filterByCategory()**: تصفية حسب الفئة

### 4. الوظائف العامة (`main.js`)
- **MainController Class**: التحكم العام في الموقع
- **toggleTheme()**: تبديل المظهر
- **handleContactForm()**: معالجة نموذج الاتصال
- **initSmoothScrolling()**: التمرير الناعم

## 🎨 التخصيص والتطوير

### الألوان الرئيسية
```css
:root {
    --primary-color: #f0a030;    /* الذهبي */
    --secondary-color: #8B4513;  /* البني */
}
```

### إضافة منتج جديد
```javascript
const newProduct = {
    id: 'unique-id',
    name: 'اسم المنتج',
    description: 'وصف المنتج',
    price: 5000,
    category: 'الفئة',
    image: 'رابط الصورة',
    featured: false,
    available: true,
    rating: 0,
    reviews: 0
};

window.productManager.addProduct(newProduct);
```

### تخصيص المظهر
يمكن تعديل ملف `assets/css/style.css` لتخصيص:
- الألوان والخطوط
- التأثيرات الحركية
- التصميم المتجاوب
- المظهر المظلم

## 📱 المحافظات المدعومة

المتجر يدعم التوصيل لجميع محافظات العراق:
- بغداد، البصرة، نينوى، أربيل
- النجف، كربلاء، السليمانية، ذي قار
- الأنبار، ديالى، كركوك، صلاح الدين
- بابل، ميسان، واسط، المثنى
- دهوك، القادسية

## 🔒 الأمان والخصوصية

### البيانات المحفوظة محلياً
- سلة التسوق
- الطلبات
- رسائل الاتصال
- إعدادات المستخدم (المظهر، اللغة)

### التحقق من البيانات
- التحقق من صحة أرقام الهواتف العراقية
- التحقق من البريد الإلكتروني
- التحقق من العناوين

## 🚧 المميزات المستقبلية

### المرحلة الأولى
- [ ] إضافة المزيد من المنتجات
- [ ] نظام تصفية وبحث متقدم
- [ ] تقييمات ومراجعات العملاء
- [ ] خاصية المفضلة

### المرحلة الثانية
- [ ] نظام إدارة المحتوى (CMS)
- [ ] حسابات المستخدمين
- [ ] تتبع الطلبات
- [ ] كوبونات الخصم

### المرحلة الثالثة
- [ ] تطبيق جوال
- [ ] نظام ولاء العملاء
- [ ] دفع إلكتروني
- [ ] لوحة تحكم الإدارة

## 👥 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. إنشاء Pull Request

### قواعد المساهمة
- كتابة كود نظيف ومفهوم
- إضافة تعليقات باللغة العربية
- اتباع نمط الكود الموجود
- اختبار التغييرات على أجهزة متعددة

## 📞 التواصل

- **الهاتف**: 07726455910
- **البريد الإلكتروني**: <EMAIL>
- **العنوان**: البصرة - شارع الجزائر - مقابل مطعم الديوان
- **وسائل التواصل**: فيسبوك، إنستجرام، تيك توك

## 📄 الترخيص

هذا المشروع مُرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🏆 الشكر والتقدير

- **Tailwind CSS** - لإطار العمل الرائع
- **RemixIcon** - للأيقونات الجميلة
- **Google Fonts** - للخطوط العربية الواضحة

---

تم تطوير هذا المشروع بـ ❤️ لخدمة عشاق الحلويات العربية الأصيلة

**© 2025 ڤيلا سويتز - جميع الحقوق محفوظة** 
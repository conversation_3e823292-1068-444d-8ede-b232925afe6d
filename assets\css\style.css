:root {
    --primary-color: #f0a030;
    --secondary-color: #8B4513;

    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #d1d5db;
    --button-secondary-bg: #f3f4f6;
    --button-secondary-text: #374151;
    --hover-bg: #f9fafb;
    --overlay-bg: rgba(0, 0, 0, 0.5);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --border-color: #374151;
    --border-light: #4b5563;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-hover: rgba(0, 0, 0, 0.4);
    --card-bg: #1f2937;
    --input-bg: #374151;
    --input-border: #4b5563;
    --button-secondary-bg: #374151;
    --button-secondary-text: #d1d5db;
    --hover-bg: #374151;
    --overlay-bg: rgba(0, 0, 0, 0.7);
}

html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Cairo', sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
    position: relative;
    width: 100%;
}

/* Container adjustments */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

/* Container padding adjustments for large screens */
@media (min-width: 1024px) {
    .container {
        max-width: 1280px;
    }
}

:where([class^="ri-"])::before { 
    content: "\f3c2"; 
}

.hero-section {
    background-image: linear-gradient(to left, rgba(0,0,0,0), rgba(255,255,255,0.95)), 
                      url('https://readdy.ai/api/search-image?query=Luxurious%20Arabic%20sweets%20and%20desserts%20arranged%20elegantly%20on%20a%20golden%20platter%20with%20decorative%20elements.%20The%20image%20has%20a%20warm%20golden%20tone%20with%20a%20clean%20white%20background%20on%20the%20left%20side%20that%20gradually%20transitions%20to%20the%20dessert%20display%20on%20the%20right.%20The%20lighting%20is%20soft%20and%20highlights%20the%20textures%20of%20the%20sweets.%20The%20composition%20is%20balanced%20and%20professional%2C%20suitable%20for%20a%20high-end%20bakery%20website.&width=1200&height=600&seq=123456&orientation=landscape');
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.cart-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    left: 0;
    z-index: 50;
    visibility: hidden;
}

.cart-sidebar.open {
    transform: translateX(0);
    visibility: visible;
}

.product-card:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .hero-section {
        background-position: center right;
    }
    
    /* Reduce padding on mobile for more space */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    /* animation: fadeIn 0.6s ease-in-out; */ /* تم تعطيل الحركة */
}

/* Loading spinner */
.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dark Mode Specific Styles */
[data-theme="dark"] .hero-section {
    background-image: linear-gradient(to left, rgba(17,24,39,0), rgba(17,24,39,0.95)),
                      url('https://readdy.ai/api/search-image?query=Luxurious%20Arabic%20sweets%20and%20desserts%20arranged%20elegantly%20on%20a%20golden%20platter%20with%20decorative%20elements.%20The%20image%20has%20a%20warm%20golden%20tone%20with%20a%20clean%20white%20background%20on%20the%20left%20side%20that%20gradually%20transitions%20to%20the%20dessert%20display%20on%20the%20right.%20The%20lighting%20is%20soft%20and%20highlights%20the%20textures%20of%20the%20sweets.%20The%20composition%20is%20balanced%20and%20professional%2C%20suitable%20for%20a%20high-end%20bakery%20website.&width=1200&height=600&seq=123456&orientation=landscape');
}

/* Product Cards Dark Mode */
[data-theme="dark"] .product-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .product-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Buttons Dark Mode */
[data-theme="dark"] .btn-primary {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .btn-secondary {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: var(--hover-bg);
}

/* Form Elements Dark Mode */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(240, 160, 48, 0.1);
}

/* Mobile Menu Dark Mode */
[data-theme="dark"] #mobile-menu {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

/* Overlay Dark Mode */
[data-theme="dark"] #overlay {
    background-color: var(--overlay-bg);
}

/* Smooth transitions for all elements */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    box-sizing: border-box;
}

/* Prevent horizontal overflow and hide any potential scrollbars */
header, nav, section, footer, main, .container {
    overflow-x: hidden;
    max-width: 100%;
}

/* Ensure no element can cause horizontal scroll */
img, video, iframe, embed, object {
    max-width: 100%;
    height: auto;
}

/* Hide horizontal scrollbar on body and html */
html {
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
}

body {
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
}

/* Remove any potential side margins that could cause scrolling */
body > * {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Ensure proper contrast for text */
[data-theme="dark"] .text-gray-500 {
    color: #9ca3af !important;
}

[data-theme="dark"] .text-gray-600 {
    color: #6b7280 !important;
}

[data-theme="dark"] .text-gray-700 {
    color: #4b5563 !important;
}

/* Hover states for dark mode */
[data-theme="dark"] .hover\:bg-gray-100:hover {
    background-color: var(--hover-bg) !important;
}

[data-theme="dark"] .hover\:bg-gray-200:hover {
    background-color: #4b5563 !important;
}

/* Accessibility improvements for dark mode */
[data-theme="dark"] {
    /* Ensure sufficient contrast ratios */
    --text-contrast-high: #ffffff;
    --text-contrast-medium: #e5e7eb;
    --text-contrast-low: #d1d5db;
}

/* Focus indicators for better accessibility */
[data-theme="dark"] button:focus,
[data-theme="dark"] a:focus,
[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    [data-theme="dark"] {
        --text-primary: #ffffff;
        --text-secondary: #ffffff;
        --border-color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Print styles for dark mode */
@media print {
    [data-theme="dark"] {
        background: white !important;
        color: black !important;
    }

    [data-theme="dark"] * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* Additional Dark Mode Enhancements */
[data-theme="dark"] .about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .product-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Global scrollbar styling */
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

/* Allow scrollbar only for specific elements that need it */
.overflow-y-auto::-webkit-scrollbar,
.overflow-y-scroll::-webkit-scrollbar {
    width: 6px;
}

.overflow-x-auto::-webkit-scrollbar,
.overflow-x-scroll::-webkit-scrollbar {
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

/* Loading spinner dark mode */
[data-theme="dark"] .loading-spinner {
    border: 3px solid var(--bg-tertiary);
    border-top: 3px solid var(--primary-color);
}

/* Cart item improvements */
.cart-item {
    gap: 1rem;
}

.cart-item img {
    border: 1px solid var(--border-light);
}

[data-theme="dark"] .cart-item img {
    border-color: var(--border-color);
}

/* Language dropdown improvements */
#language-dropdown {
    backdrop-filter: blur(8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] #language-dropdown {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

#language-dropdown a {
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

#language-dropdown a:hover {
    transform: translateX(4px);
}

/* Cart quantity button disabled state */
.decrease-quantity:disabled,
.decrease-quantity.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.decrease-quantity:disabled:hover,
.decrease-quantity.disabled:hover {
    background-color: inherit;
    transform: none;
}

/* Logo styling */
.logo-container img {
    max-height: 64px;
    width: auto;
    object-fit: contain;
}

/* Responsive logo adjustments */
@media (max-width: 768px) {
    .logo-container img {
        max-height: 56px;
    }
}

/* Dark mode logo adjustments if needed */
[data-theme="dark"] .logo-container img {
    /* Add any dark mode specific logo adjustments here if needed */
    filter: brightness(1.1);
}

/* Page animations */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer link hover effects */
.footer-link {
    position: relative;
    overflow: hidden;
}

.footer-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.footer-link:hover::before {
    width: 100%;
}

/* Content sections styling */
.content-section {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin-bottom: 2rem;
}

/* Prose styling for dark mode */
[data-theme="dark"] .prose {
    color: var(--text-primary);
}

[data-theme="dark"] .prose h1,
[data-theme="dark"] .prose h2,
[data-theme="dark"] .prose h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .prose p {
    color: var(--text-secondary);
}

[data-theme="dark"] .prose ul li {
    color: var(--text-secondary);
}

/* Payment Methods Styling */
.payment-method-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.15);
    transform: translateY(-2px);
}

[data-theme="dark"] .payment-method-card {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-color: #6b7280;
}

[data-theme="dark"] .payment-method-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.25);
}

/* Removed Mastercard styles - Cash on Delivery only */

/* Cash on Delivery Icon */
.cod-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 50%;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* Add to Cart Button Animations */
.add-to-cart {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 160, 48, 0.3);
}

.add-to-cart:active {
    transform: scale(0.98);
}

/* Button ripple effect */
.add-to-cart::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.add-to-cart:active::before {
    width: 300px;
    height: 300px;
}

/* Success notification animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

/* Floating cart icon animation */
@keyframes floatToCart {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.3) rotate(360deg);
        opacity: 0;
    }
}

.float-to-cart {
    animation: floatToCart 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Cart count bounce animation */
@keyframes bounceCount {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.cart-count-bounce {
    animation: none !important;
}

/* Success state for buttons */
.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

/* Favorites Button Styling */
.favorite-btn {
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.favorite-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.favorite-btn:active {
    transform: scale(0.95);
}

/* Favorite heart animation */
@keyframes heartBeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

.heart-beat {
    animation: heartBeat 1s ease-in-out;
}

/* Floating heart animation */
@keyframes floatingHeart {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-30px) scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-60px) scale(0.5);
        opacity: 0;
    }
}

.floating-heart {
    animation: floatingHeart 1s ease-out;
}

/* Favorites count badge */
#favorites-count {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Product card favorite button positioning - Always visible */
.product-card .favorite-btn {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
    z-index: 10;
}

.product-card .favorite-btn:hover {
    transform: scale(1.1);
}

/* Favorite notification styling */
.favorite-notification {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Heart icon transitions */
.ri-heart-line, .ri-heart-fill {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ri-heart-fill {
    color: #ef4444;
    filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.3));
}

/* Favorites Sidebar Styling */
.favorites-sidebar {
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    right: 0;
    left: auto;
    visibility: hidden;
}

.favorites-sidebar.open {
    transform: translateX(0);
    visibility: visible;
}

/* RTL Support for favorites sidebar */
[dir="rtl"] .favorites-sidebar {
    transform: translateX(100%);
    right: 0;
    left: auto;
}

[dir="rtl"] .favorites-sidebar.open {
    transform: translateX(0);
}

/* Favorite item styling */
.favorite-item {
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.favorite-item:hover {
    border-color: #f0a030;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.15);
}

[data-theme="dark"] .favorite-item:hover {
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.25);
}

/* Line clamp utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Favorite buttons in sidebar */
.add-to-cart-from-favorites {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.add-to-cart-from-favorites:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(240, 160, 48, 0.3);
}

.remove-from-favorites {
    padding: 0.375rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.remove-from-favorites:hover {
    background-color: rgba(239, 68, 68, 0.1);
    transform: scale(1.1);
}

/* Empty state styling */
.favorites-sidebar .ri-3x {
    font-size: 3rem;
}

/* Clear favorites button */
#clear-favorites-btn:hover {
    background-color: #dc2626;
    transform: scale(1.02);
}

/* Ensure favorites sidebar is properly positioned */
#favorites-sidebar {
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#favorites-sidebar.open {
    transform: translateX(0) !important;
}

/* Logo Text Styling - Responsive */
.font-andaluzia {
    font-family: 'Dancing Script', 'Andaluzia Personal Use', cursive !important;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(240, 160, 48, 0.3);
    letter-spacing: 1px;
    transition: all 0.3s ease;
    font-size: clamp(1.5rem, 4vw, 2rem); /* Responsive font size */
}

.logo-container:hover .font-andaluzia {
    transform: scale(1.05);
    text-shadow: 0 3px 6px rgba(240, 160, 48, 0.4);
}

/* Dark theme logo styling */
[data-theme="dark"] .font-andaluzia {
    color: #f0a030;
    text-shadow: 0 2px 4px rgba(240, 160, 48, 0.4);
}

[data-theme="dark"] .logo-container:hover .font-andaluzia {
    text-shadow: 0 3px 6px rgba(240, 160, 48, 0.5);
}

/* Header responsive design */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: nowrap;
    padding: 0.5rem 1rem;
}

/* Fixed header styling */
header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header animation on scroll */
@supports (backdrop-filter: blur(10px)) {
    header {
        background-color: rgba(255, 255, 255, 0.95) !important;
    }
    
    [data-theme="dark"] header {
        background-color: rgba(17, 24, 39, 0.95) !important;
    }
}

/* Logo section */
.logo-section {
    flex-shrink: 0;
    min-width: 0;
}

/* Header actions responsive */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-shrink: 0;
}

/* Header buttons responsive */
.header-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0;
    border: none;
    background: transparent;
    cursor: pointer;
}

.header-btn:hover {
    transform: scale(1.1);
}

.header-btn:focus {
    outline: 2px solid #f0a030;
    outline-offset: 2px;
}

.header-btn i {
    font-size: 1.25rem;
    color: inherit;
}

/* Badge styling for cart and favorites */
.header-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: bold;
    line-height: 1;
    padding: 0 4px;
    border: 2px solid white;
}

/* Dark theme header button colors */
[data-theme="dark"] .header-btn {
    color: #d1d5db;
}

[data-theme="dark"] .header-btn:hover {
    color: #f9fafb;
}

[data-theme="dark"] .header-badge {
    border-color: #111827;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .header-container {
        padding: 0.5rem 0.75rem;
        gap: 0.25rem;
    }
    
    /* Adjust body padding for mobile */
    body {
        padding-top: 100px !important;
    }
    
    .font-andaluzia {
        font-size: clamp(1.25rem, 5vw, 1.5rem);
    }
    
    .header-actions {
        gap: 0.25rem;
    }
    
    .header-btn {
        width: 32px;
        height: 32px;
        min-width: 32px; /* Prevent shrinking */
    }
    
    .header-btn i {
        font-size: 1.125rem;
    }
    
    .header-badge {
        min-width: 16px;
        height: 16px;
        font-size: 0.625rem;
        top: -1px;
        right: -1px;
        border-width: 1px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .header-container {
        padding: 0.25rem 0.5rem;
    }
    
    /* Further adjust body padding for very small screens */
    body {
        padding-top: 90px !important;
    }
    
    .font-andaluzia {
        font-size: clamp(1rem, 6vw, 1.25rem);
    }
    
    .header-actions {
        gap: 0.125rem;
    }
    
    .header-btn {
        width: 28px;
        height: 28px;
        min-width: 28px; /* Prevent shrinking */
    }
    
    .header-btn i {
        font-size: 0.9rem;
    }
    
    .header-badge {
        min-width: 12px;
        height: 12px;
        font-size: 0.4rem;
        border-width: 1px;
    }
}

/* Navigation responsive */
.nav-container {
    padding: 0 1rem;
}

/* Smooth scroll with offset for fixed header */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 110px;
}

@media (max-width: 640px) {
    html {
        scroll-padding-top: 100px;
    }
}

@media (max-width: 480px) {
    html {
        scroll-padding-top: 90px;
    }
}

.nav-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.nav-item {
    padding: 0.5rem 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
}

.nav-item:hover {
    transform: translateY(-1px);
}

.nav-item.active {
    border-bottom-color: #f0a030;
    color: #f0a030;
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.5rem;
    }
    
    .nav-menu {
        gap: 0.75rem;
        padding: 0.25rem 0;
    }
    
    .nav-item {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .nav-menu {
        gap: 0.5rem;
        justify-content: space-around;
    }
    
    .nav-item {
        padding: 0.5rem 0.125rem;
        font-size: 0.8rem;
        min-width: 0;
        text-align: center;
    }
}

/* Language dropdown responsive */
#language-dropdown {
    min-width: 160px;
    max-width: 200px;
    z-index: 99999 !important;
    position: absolute !important;
    top: 100% !important;
}

@media (max-width: 640px) {
    #language-dropdown {
        min-width: 140px;
        font-size: 0.875rem;
        right: 0;
        left: auto;
    }
}

/* Fix button text colors */
.header-btn {
    color: #374151;
}

[data-theme="dark"] .header-btn {
    color: #d1d5db !important;
}

.header-btn:hover {
    color: #111827;
}

[data-theme="dark"] .header-btn:hover {
    color: #f9fafb !important;
}

/* Ensure icons inherit button color */
.header-btn i {
    color: inherit !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    #favorites-sidebar {
        width: 100% !important;
        right: 0;
        left: 0;
    }
}

/* Custom Modal Styling */
#custom-modal {
    backdrop-filter: blur(4px);
    z-index: 9999;
}

#modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] #modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modal animations */
#modal-content {
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.95) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Modal button hover effects */
#modal-cancel:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#modal-confirm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Modal icon animations */
#modal-icon i {
    animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Modal responsive design */
@media (max-width: 640px) {
    #modal-content {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }

    #modal-title {
        font-size: 1.25rem;
    }

    #modal-message {
        font-size: 1rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    #modal-cancel,
    #modal-confirm {
        width: 100%;
        justify-content: center;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #modal-content {
        border: 2px solid var(--primary-color);
    }

    #modal-cancel {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #modal-content {
        animation: none;
        transition: none;
    }

    #modal-icon i {
        animation: none;
    }
}

/* Checkout modal close button */
#close-checkout {
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

#close-checkout:hover {
    transform: scale(1.1);
}

#close-checkout:active {
    transform: scale(0.95);
}

/* Ensure checkout modal is properly layered */
#checkout-modal {
    z-index: 9998;
}

#checkout-modal .bg-white {
    position: relative;
    z-index: 9999;
}

/* Authentication Modal Styling */
#auth-modal, #account-modal {
    backdrop-filter: blur(4px);
    z-index: 9999;
}

#auth-modal-content, #account-modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-height: 90vh;
    overflow-y: auto;
}

[data-theme="dark"] #auth-modal-content,
[data-theme="dark"] #account-modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Auth tabs styling */
.auth-tab {
    transition: all 0.2s ease;
}

.auth-tab:hover {
    background-color: rgba(240, 160, 48, 0.1);
}

/* Form styling enhancements */
.auth-form input:focus,
.auth-form select:focus {
    border-color: #f0a030;
    box-shadow: 0 0 0 3px rgba(240, 160, 48, 0.1);
}

.auth-form input.border-red-500 {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Password toggle button */
.password-toggle {
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.password-toggle:hover {
    color: #f0a030;
}

/* User avatar styling */
.user-avatar {
    background: linear-gradient(135deg, #f0a030, #e09020);
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.3);
}

/* Account info cards */
.account-info-card {
    transition: all 0.2s ease;
}

.account-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .account-info-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Password strength indicator */
.password-strength-bar {
    height: 4px;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Responsive adjustments for auth modals */
@media (max-width: 640px) {
    #auth-modal-content,
    #account-modal-content {
        margin: 1rem;
        max-width: calc(100% - 2rem);
        max-height: calc(100vh - 2rem);
    }

    .auth-form .grid-cols-2 {
        grid-template-columns: 1fr;
    }

    .auth-tab {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}

/* Animation for modal content */
#auth-modal-content,
#account-modal-content {
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.95) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Loading state for forms */
.auth-form.loading {
    pointer-events: none;
    opacity: 0.7;
}

.auth-form.loading button[type="submit"] {
    position: relative;
}

.auth-form.loading button[type="submit"]:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* High contrast mode support for auth */
@media (prefers-contrast: high) {
    #auth-modal-content,
    #account-modal-content {
        border: 2px solid var(--primary-color);
    }

    .auth-form input,
    .auth-form select {
        border-width: 2px;
    }
}

/* Reduced motion support for auth */
@media (prefers-reduced-motion: reduce) {
    #auth-modal-content,
    #account-modal-content {
        animation: none;
        transition: none;
    }

    .auth-tab,
    .account-info-card {
        transition: none;
    }
}

/* Mobile menu disabled for small devices */
#menu-toggle,
#mobile-menu {
    display: none !important;
}

/* Ensure mobile menu is completely hidden and doesn't affect layout */
#mobile-menu {
    visibility: hidden;
    pointer-events: none;
}

/* Final override to ensure no scrollbars or side strips appear */
@media screen {
    html, body {
        overflow-x: hidden !important;
        overflow-y: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: 100vw !important;
        -ms-overflow-style: none !important;
        scrollbar-width: none !important;
    }
    
    html::-webkit-scrollbar,
    body::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
    }
    
    /* Ensure sidebars are completely out of view when closed */
    .cart-sidebar {
        transition: transform 0.3s ease !important;
    }
    
    .favorites-sidebar {
        transition: transform 0.3s ease !important;
    }
    
    .cart-sidebar:not(.open) {
        transform: translateX(-100%) !important;
    }
    
    .favorites-sidebar:not(.open) {
        transform: translateX(100%) !important;
    }
    
    .cart-sidebar.open {
        transform: translateX(0) !important;
    }
    
    .favorites-sidebar.open {
        transform: translateX(0) !important;
    }
}




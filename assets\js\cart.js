/**
 * Cart Management System
 * Handles all cart functionality including add, remove, update items
 */

class CartManager {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('cart')) || [];
        this.initElements();
        this.initEventListeners();
        this.updateCart();
    }

    initElements() {
        this.cartToggle = document.getElementById('cart-toggle');
        this.cartSidebar = document.getElementById('cart-sidebar');
        this.closeCart = document.getElementById('close-cart');
        this.overlay = document.getElementById('overlay');
        this.cartItems = document.getElementById('cart-items');
        this.cartTotal = document.getElementById('cart-total');
        this.cartCount = document.getElementById('cart-count');
        this.checkoutBtn = document.getElementById('checkout-btn');


    }

    initEventListeners() {
        // Toggle cart sidebar
        if (this.cartToggle) {
            this.cartToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.openCart();
            });
        }

        if (this.closeCart) {
            this.closeCart.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeCartSidebar();
            });
        }

        if (this.overlay) {
            this.overlay.addEventListener('click', (e) => {
                this.closeAllModals();
            });
        }

        // Add to cart buttons (using event delegation)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart') || e.target.closest('.add-to-cart')) {
                e.preventDefault();
                const button = e.target.classList.contains('add-to-cart') ? e.target : e.target.closest('.add-to-cart');
                this.addToCart(button);
            }
        });
    }

    addToCart(button) {
        const productData = {
            id: button.dataset.id,
            name: button.dataset.name,
            price: parseInt(button.dataset.price),
            image: button.dataset.image
        };

        const existingItem = this.cart.find(item => item.id === productData.id);

        if (existingItem) {
            existingItem.quantity++;
        } else {
            this.cart.push({
                ...productData,
                quantity: 1
            });
        }

        this.updateCart();
        this.saveCart();

        // Show success animation instead of opening cart
        this.showAddToCartAnimation(button);
        this.showSuccessNotification(productData.name);
        this.animateCartCount();
    }

    removeFromCart(index) {
        this.cart.splice(index, 1);
        this.updateCart();
        this.saveCart();
    }

    updateQuantity(index, newQuantity) {
        if (newQuantity < 1) {
            // Don't allow quantity less than 1
            if (window.mainController) {
                window.mainController.showNotification('الحد الأدنى للكمية هو 1', 'warning');
            }
            return;
        }

        this.cart[index].quantity = newQuantity;
        this.updateCart();
        this.saveCart();
    }

    updateCart() {
        if (!this.cartItems || !this.cartCount || !this.cartTotal) return;

        if (this.cart.length === 0) {
            this.renderEmptyCart();
        } else {
            this.renderCartItems();
        }

        this.updateCartSummary();
    }

    renderEmptyCart() {
        this.cartItems.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <i class="ri-shopping-cart-line ri-3x mb-4 opacity-50"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        if (this.checkoutBtn) {
            this.checkoutBtn.disabled = true;
            this.checkoutBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }

    renderCartItems() {
        let cartHTML = '';
        
        this.cart.forEach((item, index) => {
            cartHTML += `
                <div class="flex items-start space-x-4 space-x-reverse bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm transition-colors duration-300">
                    <img src="${item.image}" alt="${item.name}" class="w-20 h-20 object-cover rounded-lg flex-shrink-0">
                    <div class="flex-1 mr-4">
                        <h4 class="text-gray-800 dark:text-gray-100 font-medium mb-1">${item.name}</h4>
                        <p class="text-primary font-bold mb-2">${item.price.toLocaleString()} د.ع</p>
                        <div class="flex items-center">
                            <button class="decrease-quantity w-8 h-8 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${item.quantity <= 1 ? 'opacity-50 cursor-not-allowed' : ''}" data-index="${index}" ${item.quantity <= 1 ? 'disabled' : ''}>
                                <i class="ri-subtract-line"></i>
                            </button>
                            <span class="mx-3 font-medium text-gray-800 dark:text-gray-100 min-w-[2rem] text-center">${item.quantity}</span>
                            <button class="increase-quantity w-8 h-8 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" data-index="${index}">
                                <i class="ri-add-line"></i>
                            </button>
                        </div>
                    </div>
                    <button class="remove-item text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors flex-shrink-0 ml-2" data-index="${index}">
                        <i class="ri-delete-bin-line ri-lg"></i>
                    </button>
                </div>
            `;
        });

        this.cartItems.innerHTML = cartHTML;
        this.attachCartItemListeners();
        
        if (this.checkoutBtn) {
            this.checkoutBtn.disabled = false;
            this.checkoutBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    attachCartItemListeners() {
        // Decrease quantity buttons
        document.querySelectorAll('.decrease-quantity').forEach(button => {
            button.addEventListener('click', () => {
                if (button.disabled) return;
                const index = parseInt(button.dataset.index);
                this.updateQuantity(index, this.cart[index].quantity - 1);
            });
        });

        // Increase quantity buttons
        document.querySelectorAll('.increase-quantity').forEach(button => {
            button.addEventListener('click', () => {
                const index = parseInt(button.dataset.index);
                this.updateQuantity(index, this.cart[index].quantity + 1);
            });
        });

        // Remove item buttons
        document.querySelectorAll('.remove-item').forEach(button => {
            button.addEventListener('click', () => {
                const index = parseInt(button.dataset.index);
                this.removeFromCart(index);
            });
        });
    }

    updateCartSummary() {
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        this.cartCount.textContent = totalItems;
        this.cartTotal.textContent = `${totalPrice.toLocaleString()} د.ع`;
    }

    openCart() {
        if (this.cartSidebar) {
            this.cartSidebar.classList.add('open');
        } else {
            return;
        }

        if (this.overlay) {
            this.overlay.classList.remove('hidden');
        }

        document.body.style.overflow = 'hidden';
    }

    closeCartSidebar() {
        if (this.cartSidebar) {
            this.cartSidebar.classList.remove('open');
        }

        if (this.overlay) {
            this.overlay.classList.add('hidden');
        }

        document.body.style.overflow = 'auto';
    }

    closeAllModals() {
        this.cartSidebar?.classList.remove('open');
        document.getElementById('checkout-modal')?.classList.add('hidden');
        document.getElementById('mobile-menu')?.classList.add('translate-x-full');
        this.overlay?.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    showAddToCartAnimation(button) {
        const originalText = button.innerHTML;
        const originalClasses = button.className;

        // Add pulse animation and success state
        button.style.transform = 'scale(0.95)';
        button.innerHTML = '<i class="ri-check-line"></i> تم الإضافة';
        button.className = 'bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-button font-medium transition-all duration-300 transform scale-105 shadow-lg';

        // Create floating animation effect
        const rect = button.getBoundingClientRect();
        const floatingIcon = document.createElement('div');
        floatingIcon.innerHTML = '<i class="ri-shopping-cart-line text-2xl text-green-500"></i>';
        floatingIcon.className = 'fixed z-50 pointer-events-none';
        floatingIcon.style.left = rect.left + rect.width / 2 + 'px';
        floatingIcon.style.top = rect.top + 'px';
        document.body.appendChild(floatingIcon);

        // Animate floating icon to cart
        const cartButton = document.getElementById('cart-toggle');
        const cartRect = cartButton.getBoundingClientRect();

        setTimeout(() => {
            floatingIcon.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            floatingIcon.style.left = cartRect.left + cartRect.width / 2 + 'px';
            floatingIcon.style.top = cartRect.top + 'px';
            floatingIcon.style.transform = 'scale(0.5)';
            floatingIcon.style.opacity = '0';
        }, 100);

        // Remove floating icon
        setTimeout(() => {
            document.body.removeChild(floatingIcon);
        }, 1000);

        // Reset button after animation (faster)
        setTimeout(() => {
            button.style.transform = '';
            button.innerHTML = originalText;
            button.className = originalClasses;
        }, 1000);
    }

    showSuccessNotification(productName) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-all duration-300';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="ri-check-circle-line text-xl ml-2"></i>
                <div>
                    <div class="font-semibold">تم إضافة المنتج للسلة</div>
                    <div class="text-sm opacity-90">${productName}</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Hide notification
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    animateCartCount() {
        // Animation disabled - no bounce effect for cart count
        return;
    }

    saveCart() {
        localStorage.setItem('cart', JSON.stringify(this.cart));
    }

    clearCart() {
        this.cart = [];
        this.updateCart();
        this.saveCart();
    }

    getCart() {
        return this.cart;
    }

    getTotalPrice() {
        return this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    }

    getTotalItems() {
        return this.cart.reduce((sum, item) => sum + item.quantity, 0);
    }
}

// Cart manager will be initialized from main HTML file
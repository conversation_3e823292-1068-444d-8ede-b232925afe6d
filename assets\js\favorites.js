/**
 * Favorites Management System
 * Handles all favorites functionality including add, remove, display
 */

class FavoritesManager {
    constructor() {
        this.favorites = JSON.parse(localStorage.getItem('favorites')) || [];
        this.initElements();
        this.initEventListeners();
        this.updateFavoritesCount();
        this.updateFavoriteButtons();
    }

    initElements() {
        this.favoritesToggle = document.getElementById('favorites-toggle');
        this.favoritesCount = document.getElementById('favorites-count');
        this.favoritesSidebar = document.getElementById('favorites-sidebar');
        this.closeFavorites = document.getElementById('close-favorites');
        this.favoritesItems = document.getElementById('favorites-items');
        this.clearFavoritesBtn = document.getElementById('clear-favorites-btn');
        this.overlay = document.getElementById('overlay');
    }

    initEventListeners() {
        // Toggle favorites sidebar
        if (this.favoritesToggle) {
            this.favoritesToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.openFavorites();
            });
        }

        // Close favorites sidebar
        if (this.closeFavorites) {
            this.closeFavorites.addEventListener('click', () => {
                this.closeFavoritesSidebar();
            });
        }

        // Clear all favorites
        if (this.clearFavoritesBtn) {
            this.clearFavoritesBtn.addEventListener('click', () => {
                this.clearAllFavorites();
            });
        }

        // Close on overlay click
        if (this.overlay) {
            this.overlay.addEventListener('click', () => {
                this.closeFavoritesSidebar();
            });
        }

        // Favorite buttons (using event delegation)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('favorite-btn') || e.target.closest('.favorite-btn')) {
                e.preventDefault();
                e.stopPropagation();
                const button = e.target.classList.contains('favorite-btn') ? e.target : e.target.closest('.favorite-btn');
                console.log('Favorite button clicked:', button); // Debug log
                this.toggleFavorite(button);
            }
        });

        // Additional event listener for better compatibility
        document.addEventListener('touchstart', (e) => {
            if (e.target.classList.contains('favorite-btn') || e.target.closest('.favorite-btn')) {
                e.preventDefault();
                const button = e.target.classList.contains('favorite-btn') ? e.target : e.target.closest('.favorite-btn');
                this.toggleFavorite(button);
            }
        });
    }

    toggleFavorite(button) {
        const productId = button.dataset.productId;
        const icon = button.querySelector('i');

        console.log('Toggle favorite for product:', productId); // Debug log

        if (this.isFavorite(productId)) {
            this.removeFromFavorites(productId);
            icon.className = 'ri-heart-line text-gray-600 dark:text-gray-300 group-hover:text-red-500 transition-colors duration-300';
            this.showFavoriteAnimation(button, 'removed');
        } else {
            this.addToFavorites(productId);
            icon.className = 'ri-heart-fill text-red-500 transition-colors duration-300';
            this.showFavoriteAnimation(button, 'added');
        }

        this.updateFavoritesCount();
        this.animateFavoritesCount();
        this.saveFavorites();

        // Update favorites sidebar if it's open
        if (this.favoritesSidebar && this.favoritesSidebar.classList.contains('open')) {
            this.renderFavoriteItems();
        }
    }

    addToFavorites(productId) {
        if (!this.isFavorite(productId)) {
            this.favorites.push(productId);
        }
    }

    removeFromFavorites(productId) {
        this.favorites = this.favorites.filter(id => id !== productId);
    }

    isFavorite(productId) {
        return this.favorites.includes(productId);
    }

    updateFavoritesCount() {
        if (this.favoritesCount) {
            const count = this.favorites.length;
            this.favoritesCount.textContent = count;
            
            if (count > 0) {
                this.favoritesCount.classList.remove('hidden');
            } else {
                this.favoritesCount.classList.add('hidden');
            }
        }
    }

    updateFavoriteButtons() {
        document.querySelectorAll('.favorite-btn').forEach(button => {
            const productId = button.dataset.productId;
            const icon = button.querySelector('i');
            
            if (this.isFavorite(productId)) {
                icon.className = 'ri-heart-fill text-red-500 transition-colors duration-300';
            } else {
                icon.className = 'ri-heart-line text-gray-600 dark:text-gray-300 group-hover:text-red-500 transition-colors duration-300';
            }
        });
    }

    showFavoriteAnimation(button, action) {
        // Create floating heart animation
        const rect = button.getBoundingClientRect();
        const floatingHeart = document.createElement('div');
        floatingHeart.innerHTML = action === 'added' ? 
            '<i class="ri-heart-fill text-2xl text-red-500"></i>' : 
            '<i class="ri-heart-line text-2xl text-gray-400"></i>';
        floatingHeart.className = 'fixed z-50 pointer-events-none';
        floatingHeart.style.left = rect.left + rect.width / 2 + 'px';
        floatingHeart.style.top = rect.top + 'px';
        document.body.appendChild(floatingHeart);
        
        // Animate floating heart
        setTimeout(() => {
            floatingHeart.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            floatingHeart.style.transform = 'translateY(-50px) scale(1.5)';
            floatingHeart.style.opacity = '0';
        }, 100);
        
        // Remove floating heart
        setTimeout(() => {
            document.body.removeChild(floatingHeart);
        }, 1000);

        // Button animation
        button.style.transform = 'scale(1.2)';
        setTimeout(() => {
            button.style.transform = '';
        }, 200);

        // Show notification
        this.showFavoriteNotification(action);
    }

    showFavoriteNotification(action) {
        let message, color, icon;

        switch(action) {
            case 'added':
                message = 'تم إضافة المنتج للمفضلة';
                color = 'bg-red-500';
                icon = 'ri-heart-fill';
                break;
            case 'removed':
                message = 'تم إزالة المنتج من المفضلة';
                color = 'bg-gray-500';
                icon = 'ri-heart-line';
                break;
            case 'cleared':
                message = 'تم مسح جميع المنتجات من المفضلة';
                color = 'bg-orange-500';
                icon = 'ri-delete-bin-line';
                break;
            default:
                message = 'تم تحديث المفضلة';
                color = 'bg-blue-500';
                icon = 'ri-heart-line';
        }
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 ${color} text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-all duration-300`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="${icon} text-xl ml-2"></i>
                <div class="font-semibold">${message}</div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Hide notification
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 2000);
    }

    showFavoritesNotification() {
        const count = this.favorites.length;
        const message = count > 0 ? `لديك ${count} منتج في المفضلة` : 'لا توجد منتجات في المفضلة';
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-all duration-300';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="ri-heart-fill text-xl ml-2"></i>
                <div class="font-semibold">${message}</div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Hide notification
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    animateFavoritesCount() {
        if (this.favoritesCount) {
            // Add bounce animation
            this.favoritesCount.classList.add('cart-count-bounce');
            
            // Remove animation class after animation completes
            setTimeout(() => {
                this.favoritesCount.classList.remove('cart-count-bounce');
            }, 600);
        }
    }

    saveFavorites() {
        localStorage.setItem('favorites', JSON.stringify(this.favorites));
    }

    getFavorites() {
        return this.favorites;
    }

    openFavorites() {
        console.log('Opening favorites sidebar...'); // Debug
        console.log('Favorites count:', this.favorites.length); // Debug

        this.renderFavoriteItems();

        if (this.favoritesSidebar) {
            this.favoritesSidebar.style.transform = 'translateX(0)';
            this.favoritesSidebar.classList.add('open');
        }

        document.body.style.overflow = 'hidden';

        // Show overlay if it exists
        if (this.overlay) {
            this.overlay.classList.remove('hidden');
        }
    }

    closeFavoritesSidebar() {
        this.favoritesSidebar.style.transform = 'translateX(100%)';
        this.favoritesSidebar.classList.remove('open');
        document.body.style.overflow = '';

        // Hide overlay if it exists
        if (this.overlay) {
            this.overlay.classList.add('hidden');
        }
    }

    renderFavoriteItems() {
        if (!this.favoritesItems) return;

        if (this.favorites.length === 0) {
            this.favoritesItems.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="ri-heart-line ri-3x mb-4 opacity-50"></i>
                    <p>لا توجد منتجات في المفضلة</p>
                </div>
            `;
            return;
        }

        // Get products data
        let products = [];
        if (window.productsManager && window.productsManager.products) {
            products = window.productsManager.products;
        } else if (window.productsManager && window.productsManager.getProducts) {
            products = window.productsManager.getProducts();
        } else {
            // Fallback: get products directly
            products = this.getProductsData();
        }

        console.log('Available products:', products); // Debug
        console.log('Favorite IDs:', this.favorites); // Debug

        const favoriteProducts = products.filter(product => this.favorites.includes(product.id));
        console.log('Filtered favorite products:', favoriteProducts); // Debug

        if (favoriteProducts.length === 0 && this.favorites.length > 0) {
            // Products exist in favorites but not found in products list
            this.favoritesItems.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    <i class="ri-error-warning-line ri-3x mb-4 opacity-50"></i>
                    <p>خطأ في تحميل المنتجات المفضلة</p>
                    <button onclick="window.favoritesManager.renderFavoriteItems()" class="mt-4 bg-primary text-white px-4 py-2 rounded-lg hover:bg-opacity-90 transition-all">
                        إعادة المحاولة
                    </button>
                </div>
            `;
            return;
        }

        this.favoritesItems.innerHTML = favoriteProducts.map(product => `
            <div class="favorite-item bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-colors duration-300">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <img src="${product.image}"
                         alt="${product.name}"
                         class="w-16 h-16 object-cover rounded-lg"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjZjBhMDMwIi8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiPuKZpTwvdGV4dD4KPHN2Zz4K'">

                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800 dark:text-gray-100 mb-1">${product.name}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">${product.description}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-primary font-bold">${product.price.toLocaleString()} د.ع</span>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <button class="add-to-cart-from-favorites bg-primary text-white px-3 py-1 rounded text-sm hover:bg-opacity-90 transition-all"
                                        data-id="${product.id}"
                                        data-name="${product.name}"
                                        data-price="${product.price}"
                                        data-image="${product.image}">
                                    <i class="ri-shopping-cart-line ml-1"></i>
                                    إضافة للسلة
                                </button>
                                <button class="remove-from-favorites text-red-500 hover:text-red-700 p-1 transition-colors"
                                        data-product-id="${product.id}">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Add event listeners for the new buttons
        this.addFavoriteItemEventListeners();
    }

    addFavoriteItemEventListeners() {
        // Add to cart from favorites
        document.querySelectorAll('.add-to-cart-from-favorites').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                if (window.cartManager) {
                    window.cartManager.addToCart(button);
                }
            });
        });

        // Remove from favorites
        document.querySelectorAll('.remove-from-favorites').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = button.dataset.productId;
                this.removeFromFavorites(productId);
                this.updateFavoritesCount();
                this.updateFavoriteButtons();
                this.saveFavorites();
                this.renderFavoriteItems(); // Re-render the list
                this.showFavoriteNotification('removed');
            });
        });
    }

    clearAllFavorites() {
        if (this.favorites.length === 0) return;

        // Show custom confirmation modal
        if (window.customModal) {
            window.customModal.deleteConfirm(
                'هل أنت متأكد من حذف جميع المنتجات من المفضلة؟ لا يمكن التراجع عن هذا الإجراء.',
                () => {
                    // On confirm
                    this.favorites = [];
                    this.updateFavoritesCount();
                    this.updateFavoriteButtons();
                    this.saveFavorites();
                    this.renderFavoriteItems();

                    // Show success notification
                    this.showFavoriteNotification('cleared');
                },
                () => {
                    // On cancel - do nothing
                    console.log('Clear favorites cancelled');
                }
            );
        } else {
            // Fallback to browser confirm if custom modal not available
            if (confirm('هل أنت متأكد من حذف جميع المنتجات من المفضلة؟')) {
                this.favorites = [];
                this.updateFavoritesCount();
                this.updateFavoriteButtons();
                this.saveFavorites();
                this.renderFavoriteItems();

                this.showFavoriteNotification('cleared');
            }
        }
    }

    getProductsData() {
        // Fallback products data - same as in products.js
        return [
            {
                id: '1',
                name: 'جوزية بالكراميل',
                description: 'قطع جوزية محشية كراميل غني ومغطاة بشوكولاتة فاخرة',
                price: 7000,
                category: 'باقلاوة',
                image: 'https://readdy.ai/api/search-image?query=Luxurious%20Arabic%20baklava%20dessert%20with%20caramel%20filling%2C%20covered%20in%20premium%20chocolate.%20The%20dessert%20is%20photographed%20from%20above%20on%20a%20clean%20white%20plate%20with%20golden%20decorative%20elements.%20The%20lighting%20is%20bright%20and%20highlights%20the%20glossy%20chocolate%20coating%20and%20the%20caramel%20drizzle.%20The%20background%20is%20minimal%20and%20elegant%2C%20focusing%20on%20the%20exquisite%20details%20of%20the%20dessert.&width=600&height=400&seq=123457&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.8,
                reviews: 24
            },
            {
                id: '2',
                name: 'مادلين كيك بالبندق',
                description: 'كيك ناعم مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
                price: 4500,
                category: 'كيك',
                image: 'https://readdy.ai/api/search-image?query=Elegant%20Madeleine%20cakes%20covered%20in%20premium%20chocolate%20and%20garnished%20with%20roasted%20hazelnuts.%20The%20cakes%20are%20arranged%20on%20a%20golden%20serving%20tray%20with%20decorative%20elements.%20The%20lighting%20is%20soft%20and%20highlights%20the%20glossy%20chocolate%20coating%20and%20the%20texture%20of%20the%20hazelnuts.%20The%20background%20is%20clean%20and%20minimal%2C%20focusing%20on%20the%20delicate%20details%20of%20the%20dessert.%20The%20composition%20is%20professional%20and%20appetizing.&width=600&height=400&seq=123458&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.6,
                reviews: 18
            },
            {
                id: '3',
                name: 'حلى ڤيلا بالفول السوداني',
                description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
                price: 6000,
                category: 'حلويات',
                image: 'https://readdy.ai/api/search-image?query=Luxurious%20layered%20Arabic%20dessert%20with%20multiple%20cream%20layers%20and%20crunchy%20peanuts.%20The%20dessert%20is%20presented%20on%20an%20elegant%20golden%20plate%20with%20decorative%20elements.%20The%20lighting%20is%20bright%20and%20highlights%20the%20different%20textures%20and%20layers%20of%20the%20dessert.%20The%20background%20is%20clean%20and%20minimal%2C%20focusing%20on%20the%20exquisite%20details%20of%20the%20dessert.%20The%20composition%20is%20professional%20and%20appetizing.&width=600&height=400&seq=123459&orientation=landscape',
                featured: true,
                available: true,
                rating: 4.9,
                reviews: 31
            }
        ];
    }

    clearFavorites() {
        this.favorites = [];
        this.updateFavoritesCount();
        this.updateFavoriteButtons();
        this.saveFavorites();
    }
}

// Favorites manager will be initialized from main HTML file

// Observer to update favorite buttons when products are rendered
function initFavoritesObserver() {
    const observer = new MutationObserver(() => {
        if (window.favoritesManager) {
            setTimeout(() => {
                window.favoritesManager.updateFavoriteButtons();
            }, 100);
        }
    });

    const productsContainer = document.querySelector('#products .grid');
    if (productsContainer) {
        observer.observe(productsContainer, { childList: true, subtree: true });
    }

    // Also update buttons after delays to ensure products are loaded
    setTimeout(() => {
        if (window.favoritesManager) {
            window.favoritesManager.updateFavoriteButtons();
        }
    }, 500);

    setTimeout(() => {
        if (window.favoritesManager) {
            window.favoritesManager.updateFavoriteButtons();
        }
    }, 1500);
}

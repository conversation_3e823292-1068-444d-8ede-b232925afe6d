# 🔧 دليل لوحة الإدارة الشاملة

## 📋 نظرة عامة

لوحة إدارة متكاملة لإدارة متجر إلكتروني شامل تتضمن:
- إدارة المنتجات والمخزون
- إدارة الطلبات والتتبع
- إدارة المستخدمين والرتب
- النشرة البريدية والإشعارات
- الإحصائيات والتقارير المفصلة
- سجل الأنشطة والمراجعة
- نظام صلاحيات متقدم

## 🛠️ المتطلبات التقنية

- HTML5, CSS3, JavaScript (ES6+)
- Font Awesome للأيقونات
- Chart.js للرسوم البيانية (اختياري)
- localStorage للتخزين المحلي
- Web APIs للوظائف المتقدمة

## 🏗️ هيكل لوحة الإدارة

### 1. **نظام التحقق من الصلاحيات**

```javascript
// فحص صلاحيات الإدارة
function checkAdminAccess() {
  if (!window.authManager || !window.authManager.isAuthenticated()) {
    showAccessDenied();
    return false;
  }

  const user = window.authManager.getCurrentUser();
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  const adminPhones = ['07726455910', '07700000000'];
  
  if (user && 
      (adminEmails.includes(user.email?.toLowerCase()) || 
       adminPhones.includes(user.phone))) {
    showAdminPanel();
    return true;
  } else {
    showAccessDenied();
    return false;
  }
}

function showAdminPanel() {
  document.getElementById('access-denied-section').style.display = 'none';
  document.getElementById('admin-panel').style.display = 'block';
}

function showAccessDenied() {
  document.getElementById('access-denied-section').style.display = 'block';
  document.getElementById('admin-panel').style.display = 'none';
}
```

### 2. **إدارة المنتجات (Products Management)**

#### **2.1 عرض المنتجات**
```javascript
function loadProducts() {
  const productsGrid = document.getElementById('admin-products-grid');
  const products = getProductsData(); // من ملف products-data.js
  
  if (products.length === 0) {
    productsGrid.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-box-open"></i>
        <h4>لا توجد منتجات</h4>
        <p>ابدأ بإضافة منتجات جديدة</p>
      </div>
    `;
    return;
  }

  productsGrid.innerHTML = products.map(product => `
    <div class="admin-product-card">
      <img src="${product.image}" alt="${product.name}" class="admin-product-image">
      <div class="admin-product-info">
        <h4 class="admin-product-title">${product.name}</h4>
        <p class="admin-product-price">${product.price.toLocaleString()} دينار</p>
        
        <div class="admin-product-badges">
          ${product.featured ? '<span class="product-badge featured">مميز</span>' : ''}
          ${product.isNew ? '<span class="product-badge new">جديد</span>' : ''}
          ${product.popular ? '<span class="product-badge popular">شائع</span>' : ''}
          ${product.onSale ? '<span class="product-badge sale">تخفيض</span>' : ''}
        </div>
        
        <div class="admin-product-actions">
          <button class="btn btn-small btn-primary" onclick="editProduct(${product.id})">
            <i class="fas fa-edit"></i> تعديل
          </button>
          <button class="btn btn-small btn-danger" onclick="deleteProduct(${product.id})">
            <i class="fas fa-trash"></i> حذف
          </button>
          <button class="btn btn-small btn-secondary" onclick="toggleFeatured(${product.id})">
            <i class="fas fa-star"></i> ${product.featured ? 'إلغاء التمييز' : 'تمييز'}
          </button>
        </div>
      </div>
    </div>
  `).join('');
}
```

#### **2.2 إضافة منتج جديد**
```javascript
function showAddProductModal() {
  const modal = document.getElementById('product-modal');
  document.getElementById('product-form').reset();
  document.getElementById('product-modal-title').textContent = 'إضافة منتج جديد';
  modal.classList.add('show');
}

async function saveProduct(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const productData = {
    id: Date.now(), // معرف مؤقت
    name: formData.get('name'),
    price: parseInt(formData.get('price')),
    description: formData.get('description'),
    category: formData.get('category'),
    image: formData.get('image') || 'images/placeholder.jpg',
    featured: formData.get('featured') === 'on',
    isNew: formData.get('isNew') === 'on',
    popular: formData.get('popular') === 'on',
    onSale: formData.get('onSale') === 'on',
    salePrice: formData.get('salePrice') ? parseInt(formData.get('salePrice')) : null,
    stock: parseInt(formData.get('stock')) || 0,
    createdAt: new Date().toISOString()
  };

  try {
    const products = getProductsData();
    products.push(productData);
    
    // حفظ في localStorage أو إرسال للخادم
    localStorage.setItem('products_data', JSON.stringify(products));
    
    hideProductModal();
    loadProducts();
    showToast('تم إضافة المنتج بنجاح', 'success');
    
    // تسجيل النشاط
    logActivity('product_add', `تم إضافة منتج جديد: ${productData.name}`, productData.id);
    
  } catch (error) {
    showToast('حدث خطأ في إضافة المنتج', 'error');
  }
}
```

### 3. **إدارة الطلبات (Orders Management)**

#### **3.1 عرض الطلبات مع الفلترة**
```javascript
function loadOrders() {
  const ordersList = document.getElementById('admin-orders-list');
  let orders = JSON.parse(localStorage.getItem('vela_orders') || '[]');
  
  // تطبيق الفلاتر
  const statusFilter = document.getElementById('order-status-filter')?.value || 'all';
  const periodFilter = document.getElementById('order-period-filter')?.value || 'all';
  
  if (statusFilter !== 'all') {
    orders = orders.filter(order => order.status === statusFilter);
  }
  
  if (periodFilter !== 'all') {
    const now = new Date();
    orders = orders.filter(order => {
      const orderDate = new Date(order.date);
      switch(periodFilter) {
        case 'today':
          return orderDate.toDateString() === now.toDateString();
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          return orderDate >= weekAgo;
        case 'month':
          return orderDate.getMonth() === now.getMonth() && 
                 orderDate.getFullYear() === now.getFullYear();
        default:
          return true;
      }
    });
  }

  if (orders.length === 0) {
    ordersList.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-shopping-cart"></i>
        <h4>لا توجد طلبات</h4>
        <p>الطلبات الجديدة ستظهر هنا</p>
      </div>
    `;
    return;
  }

  // ترتيب الطلبات (الأحدث أولاً)
  orders.sort((a, b) => new Date(b.date) - new Date(a.date));

  ordersList.innerHTML = orders.map(order => `
    <div class="order-item">
      <div class="item-header">
        <div class="item-title">طلب #${order.id}</div>
        <span class="status-badge ${order.status}">${getStatusText(order.status)}</span>
      </div>
      
      <div class="item-details">
        <p><strong>العميل:</strong> ${order.customerName}</p>
        <p><strong>الهاتف:</strong> ${order.customerPhone}</p>
        <p><strong>المحافظة:</strong> ${order.province}</p>
        <p><strong>المجموع:</strong> ${order.total.toLocaleString()} دينار عراقي</p>
        <p><strong>التاريخ:</strong> ${new Date(order.date).toLocaleDateString('ar-IQ')}</p>
        <p><strong>المنتجات:</strong> ${order.items.map(item => `${item.name} (${item.quantity})`).join(', ')}</p>
      </div>
      
      <div class="item-actions">
        ${order.status === 'pending' ? `
          <button class="btn btn-success btn-small" onclick="approveOrder('${order.id}')">
            <i class="fas fa-check"></i> قبول
          </button>
          <button class="btn btn-danger btn-small" onclick="rejectOrder('${order.id}')">
            <i class="fas fa-times"></i> رفض
          </button>
        ` : ''}
        
        <button class="btn btn-info btn-small" onclick="viewOrderDetails('${order.id}')">
          <i class="fas fa-eye"></i> التفاصيل
        </button>
        
        <button class="btn btn-warning btn-small" onclick="updateOrderStatus('${order.id}')">
          <i class="fas fa-edit"></i> تحديث الحالة
        </button>
        
        <button class="btn btn-danger btn-small" onclick="deleteOrder('${order.id}')">
          <i class="fas fa-trash"></i> حذف
        </button>
      </div>
    </div>
  `).join('');
  
  // تحديث الإحصائيات
  updateOrdersStats();
}
```

#### **3.2 إحصائيات الطلبات**
```javascript
function updateOrdersStats() {
  const orders = JSON.parse(localStorage.getItem('vela_orders') || '[]');
  const now = new Date();
  
  // طلبات اليوم
  const todayOrders = orders.filter(order => 
    new Date(order.date).toDateString() === now.toDateString()
  );
  
  // طلبات الأسبوع
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const weekOrders = orders.filter(order => new Date(order.date) >= weekAgo);
  
  // طلبات الشهر
  const monthOrders = orders.filter(order => {
    const orderDate = new Date(order.date);
    return orderDate.getMonth() === now.getMonth() && 
           orderDate.getFullYear() === now.getFullYear();
  });

  // حساب الإيرادات
  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const weekRevenue = weekOrders.reduce((sum, order) => sum + order.total, 0);
  const monthRevenue = monthOrders.reduce((sum, order) => sum + order.total, 0);
  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);

  // تحديث العرض
  document.getElementById('today-orders').textContent = todayOrders.length;
  document.getElementById('today-revenue').textContent = `${todayRevenue.toLocaleString()} د.ع`;
  
  document.getElementById('week-orders').textContent = weekOrders.length;
  document.getElementById('week-revenue').textContent = `${weekRevenue.toLocaleString()} د.ع`;
  
  document.getElementById('month-orders').textContent = monthOrders.length;
  document.getElementById('month-revenue').textContent = `${monthRevenue.toLocaleString()} د.ع`;
  
  document.getElementById('total-orders').textContent = orders.length;
  document.getElementById('total-revenue-display').textContent = `${totalRevenue.toLocaleString()} د.ع`;
}
```

### 4. **تتبع الطلبات (Order Tracking)**

```javascript
function loadTracking() {
  const trackingList = document.getElementById('admin-tracking-list');
  const orders = JSON.parse(localStorage.getItem('vela_orders') || '[]')
    .filter(order => order.status !== 'pending');

  if (orders.length === 0) {
    trackingList.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-truck"></i>
        <h4>لا توجد طلبات للتتبع</h4>
        <p>الطلبات المؤكدة ستظهر هنا للتتبع</p>
      </div>
    `;
    return;
  }

  trackingList.innerHTML = orders.map(order => `
    <div class="tracking-item">
      <div class="item-header">
        <div class="item-title">طلب #${order.id} - ${order.customerName}</div>
        <div class="tracking-controls">
          <select class="status-selector" onchange="updateOrderStatus('${order.id}', this.value)">
            <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>مؤكد</option>
            <option value="preparing" ${order.status === 'preparing' ? 'selected' : ''}>قيد التجهيز</option>
            <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>قيد التوصيل</option>
            <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>تم التسليم</option>
            <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>ملغي</option>
          </select>
        </div>
      </div>
      
      <div class="item-details">
        <p><strong>الهاتف:</strong> ${order.customerPhone}</p>
        <p><strong>العنوان:</strong> ${order.address}</p>
        <p><strong>المجموع:</strong> ${order.total.toLocaleString()} د.ع</p>
        <p><strong>آخر تحديث:</strong> ${new Date(order.lastUpdate || order.date).toLocaleDateString('ar-IQ')}</p>
      </div>
    </div>
  `).join('');
}

function updateOrderStatus(orderId, newStatus) {
  const orders = JSON.parse(localStorage.getItem('vela_orders') || '[]');
  const orderIndex = orders.findIndex(o => o.id === orderId);
  
  if (orderIndex !== -1) {
    const oldStatus = orders[orderIndex].status;
    orders[orderIndex].status = newStatus;
    orders[orderIndex].lastUpdate = new Date().toISOString();
    
    // إذا تم التسليم، تحديث رتبة المستخدم
    if (newStatus === 'delivered') {
      updateUserRank(orders[orderIndex].customerId);
    }
    
    localStorage.setItem('vela_orders', JSON.stringify(orders));
    
    loadTracking();
    loadOrders();
    
    showToast(`تم تحديث حالة الطلب إلى: ${getStatusText(newStatus)}`, 'success');
    
    // تسجيل النشاط
    logActivity('order_status', 
      `تغيير حالة الطلب ${orderId} من "${getStatusText(oldStatus)}" إلى "${getStatusText(newStatus)}"`, 
      orderId);
    
    // إرسال إشعار للعميل
    sendOrderNotification(orders[orderIndex], newStatus);
  }
}
```

### 5. **إدارة المستخدمين (Users Management)**

#### **5.1 عرض المستخدمين مع الإحصائيات**
```javascript
function loadUsers() {
  const usersList = document.getElementById('admin-users-list');
  const users = JSON.parse(localStorage.getItem('vela_users') || '[]');
  const orders = JSON.parse(localStorage.getItem('vela_orders') || '[]');
  
  if (users.length === 0) {
    usersList.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-users"></i>
        <h4>لا يوجد مستخدمين</h4>
        <p>المستخدمين المسجلين سيظهرون هنا</p>
      </div>
    `;
    return;
  }

  // حساب إحصائيات كل مستخدم
  const usersWithStats = users.map(user => {
    const userOrders = orders.filter(order => order.customerId === user.userId);
    const completedOrders = userOrders.filter(order => order.status === 'delivered');
    const totalSpent = completedOrders.reduce((sum, order) => sum + order.total, 0);
    
    return {
      ...user,
      ordersCount: userOrders.length,
      completedOrders: completedOrders.length,
      totalSpent: totalSpent,
      lastOrderDate: userOrders.length > 0 ? 
        new Date(Math.max(...userOrders.map(o => new Date(o.date)))).toLocaleDateString('ar-IQ') : 
        'لا توجد طلبات'
    };
  });

  // فلترة حسب المحافظة والرتبة
  let filteredUsers = usersWithStats;
  const provinceFilter = document.getElementById('province-filter')?.value;
  const rankFilter = document.getElementById('rank-filter')?.value;
  
  if (provinceFilter && provinceFilter !== 'all') {
    filteredUsers = filteredUsers.filter(user => user.province === provinceFilter);
  }
  
  if (rankFilter && rankFilter !== 'all') {
    filteredUsers = filteredUsers.filter(user => user.rank === rankFilter);
  }

  usersList.innerHTML = filteredUsers.map(user => `
    <div class="user-item">
      <div class="item-header">
        <div class="item-title">${user.name}</div>
        <div class="user-rank ${user.rank}">
          <i class="fas ${user.rank === 'premium' ? 'fa-crown' : 'fa-user'}"></i>
          ${user.rank === 'premium' ? 'عضو مميز' : 'عضو عادي'}
        </div>
      </div>
      
      <div class="item-details">
        <p><strong>المعرف:</strong> ${user.userId}</p>
        <p><strong>البريد:</strong> ${user.email}</p>
        <p><strong>الهاتف:</strong> ${user.phone}</p>
        <p><strong>المحافظة:</strong> ${user.province}</p>
        <p><strong>تاريخ التسجيل:</strong> ${new Date(user.createdAt).toLocaleDateString('ar-IQ')}</p>
        <p><strong>عدد الطلبات:</strong> ${user.ordersCount}</p>
        <p><strong>الطلبات المكتملة:</strong> ${user.completedOrders}</p>
        <p><strong>إجمالي المشتريات:</strong> ${user.totalSpent.toLocaleString()} د.ع</p>
        <p><strong>آخر طلب:</strong> ${user.lastOrderDate}</p>
      </div>
      
      <div class="item-actions">
        <button class="btn btn-primary btn-small" onclick="changeUserRank('${user.userId}')">
          <i class="fas fa-crown"></i> تغيير الرتبة
        </button>
        <button class="btn btn-info btn-small" onclick="viewUserDetails('${user.userId}')">
          <i class="fas fa-eye"></i> التفاصيل
        </button>
        <button class="btn btn-warning btn-small" onclick="sendNotificationToUser('${user.userId}')">
          <i class="fas fa-bell"></i> إرسال إشعار
        </button>
        <button class="btn btn-danger btn-small" onclick="deleteUser('${user.userId}')">
          <i class="fas fa-trash"></i> حذف
        </button>
      </div>
    </div>
  `).join('');
  
  // تحديث الإحصائيات العامة
  updateUserStatistics(filteredUsers);
}
```

### 6. **النشرة البريدية (Newsletter Management)**

```javascript
function loadNewsletter() {
  const subscribersList = document.getElementById('subscribers-list');
  const subscribers = JSON.parse(localStorage.getItem('newsletter_subscribers') || '[]');
  
  if (subscribers.length === 0) {
    subscribersList.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-envelope"></i>
        <h4>لا يوجد مشتركين</h4>
        <p>المشتركين في النشرة البريدية سيظهرون هنا</p>
      </div>
    `;
    return;
  }

  subscribersList.innerHTML = subscribers.map(subscriber => `
    <div class="subscriber-item">
      <div class="subscriber-info">
        <h4>${subscriber.email}</h4>
        <p>تاريخ الاشتراك: ${new Date(subscriber.subscribedAt).toLocaleDateString('ar-IQ')}</p>
        <span class="status-badge ${subscriber.active ? 'active' : 'inactive'}">
          ${subscriber.active ? 'نشط' : 'غير نشط'}
        </span>
      </div>
      <div class="subscriber-actions">
        <button class="btn btn-small ${subscriber.active ? 'btn-warning' : 'btn-success'}" 
                onclick="toggleSubscriberStatus('${subscriber.email}')">
          <i class="fas ${subscriber.active ? 'fa-pause' : 'fa-play'}"></i>
          ${subscriber.active ? 'إيقاف' : 'تفعيل'}
        </button>
        <button class="btn btn-small btn-danger" onclick="removeSubscriber('${subscriber.email}')">
          <i class="fas fa-trash"></i> حذف
        </button>
      </div>
    </div>
  `).join('');
  
  // تحديث إحصائيات النشرة البريدية
  updateNewsletterStats(subscribers);
}

function exportSubscribers() {
  const subscribers = JSON.parse(localStorage.getItem('newsletter_subscribers') || '[]');
  
  if (subscribers.length === 0) {
    showToast('لا يوجد مشتركين للتصدير', 'warning');
    return;
  }

  // إنشاء ملف CSV
  const csvContent = [
    ['البريد الإلكتروني', 'تاريخ الاشتراك', 'الحالة'],
    ...subscribers.map(sub => [
      sub.email,
      new Date(sub.subscribedAt).toLocaleDateString('ar-IQ'),
      sub.active ? 'نشط' : 'غير نشط'
    ])
  ].map(row => row.join(',')).join('\n');

  // تحميل الملف
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
  
  showToast('تم تصدير قائمة المشتركين بنجاح', 'success');
}
```

### 7. **سجل الأنشطة (Activity Logs)**

```javascript
function logActivity(type, description, relatedId = null) {
  const logs = JSON.parse(localStorage.getItem('admin_logs') || '[]');
  const currentUser = window.authManager ? window.authManager.getCurrentUser() : null;
  
  const logEntry = {
    id: 'LOG' + Date.now(),
    timestamp: new Date().toISOString(),
    type: type,
    description: description,
    relatedId: relatedId,
    adminUser: currentUser ? currentUser.name : 'مدير النظام',
    adminEmail: currentUser ? currentUser.email : '<EMAIL>'
  };
  
  logs.unshift(logEntry); // إضافة في البداية
  
  // الاحتفاظ بآخر 1000 سجل فقط
  if (logs.length > 1000) {
    logs.splice(1000);
  }
  
  localStorage.setItem('admin_logs', JSON.stringify(logs));
}

function loadLogs() {
  const logsList = document.getElementById('admin-logs-list');
  const logs = JSON.parse(localStorage.getItem('admin_logs') || '[]');
  
  // تطبيق الفلاتر
  const searchTerm = document.getElementById('logs-search')?.value?.toLowerCase() || '';
  const filterType = document.getElementById('logs-filter')?.value || 'all';

  let filteredLogs = logs;

  if (searchTerm) {
    filteredLogs = logs.filter(log => 
      log.description.toLowerCase().includes(searchTerm) ||
      (log.relatedId && log.relatedId.toLowerCase().includes(searchTerm)) ||
      log.adminUser.toLowerCase().includes(searchTerm)
    );
  }

  if (filterType !== 'all') {
    filteredLogs = filteredLogs.filter(log => log.type.startsWith(filterType));
  }

  if (filteredLogs.length === 0) {
    logsList.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-clipboard-list"></i>
        <h4>لا توجد سجلات</h4>
        <p>سجلات الأنشطة ستظهر هنا</p>
      </div>
    `;
    return;
  }

  logsList.innerHTML = filteredLogs.map(log => `
    <div class="log-item">
      <div class="log-header">
        <div class="log-icon">
          <i class="fas fa-${getLogIcon(log.type)}"></i>
        </div>
        <div class="log-info">
          <h4>${getLogTypeText(log.type)}</h4>
          <p class="log-time">${new Date(log.timestamp).toLocaleString('ar-IQ')}</p>
        </div>
        <div class="log-admin">
          <span>بواسطة: ${log.adminUser}</span>
        </div>
      </div>
      <div class="log-description">
        ${log.description}
        ${log.relatedId ? `<span class="log-id">(معرف: ${log.relatedId})</span>` : ''}
      </div>
    </div>
  `).join('');
}
```

### 8. **الإحصائيات والتقارير**

```javascript
function generateAdvancedReport() {
  const users = JSON.parse(localStorage.getItem('vela_users') || '[]');
  const orders = JSON.parse(localStorage.getItem('vela_orders') || '[]');
  const subscribers = JSON.parse(localStorage.getItem('newsletter_subscribers') || '[]');
  
  const report = {
    // إحصائيات عامة
    totalUsers: users.length,
    totalOrders: orders.length,
    totalRevenue: orders.reduce((sum, order) => sum + order.total, 0),
    totalSubscribers: subscribers.length,
    
    // إحصائيات الطلبات حسب الحالة
    ordersByStatus: {
      pending: orders.filter(o => o.status === 'pending').length,
      confirmed: orders.filter(o => o.status === 'confirmed').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      rejected: orders.filter(o => o.status === 'rejected').length
    },
    
    // إحصائيات المحافظات
    provinceStats: getProvinceStatistics(users, orders),
    
    // أفضل العملاء
    topCustomers: getTopCustomers(users, orders),
    
    // الإيرادات الشهرية
    monthlyRevenue: getMonthlyRevenue(orders),
    
    // معدل التحويل
    conversionRate: calculateConversionRate(users, orders)
  };
  
  return report;
}

function getProvinceStatistics(users, orders) {
  const provinces = {};
  
  users.forEach(user => {
    if (!provinces[user.province]) {
      provinces[user.province] = {
        users: 0,
        orders: 0,
        revenue: 0
      };
    }
    provinces[user.province].users++;
  });
  
  orders.forEach(order => {
    if (provinces[order.province]) {
      provinces[order.province].orders++;
      provinces[order.province].revenue += order.total;
    }
  });
  
  return provinces;
}

function displayDashboardStats() {
  const report = generateAdvancedReport();
  
  // تحديث البطاقات الإحصائية
  document.getElementById('total-users').textContent = report.totalUsers;
  document.getElementById('total-orders').textContent = report.totalOrders;
  document.getElementById('total-revenue').textContent = `${report.totalRevenue.toLocaleString()} د.ع`;
  document.getElementById('total-subscribers').textContent = report.totalSubscribers;
  
  // عرض أفضل محافظة
  const topProvince = Object.entries(report.provinceStats)
    .sort(([,a], [,b]) => b.revenue - a.revenue)[0];
  
  if (topProvince) {
    document.getElementById('top-province').textContent = topProvince[0];
  }
  
  // عرض الطلبات المكتملة
  document.getElementById('completed-orders').textContent = report.ordersByStatus.delivered;
}
```

## 🎯 الميزات المتقدمة

### 1. **نظام الإشعارات المتقدم**
```javascript
class NotificationSystem {
  constructor() {
    this.notifications = JSON.parse(localStorage.getItem('admin_notifications') || '[]');
  }
  
  addNotification(title, message, type = 'info', urgent = false) {
    const notification = {
      id: Date.now(),
      title: title,
      message: message,
      type: type,
      urgent: urgent,
      timestamp: new Date().toISOString(),
      read: false
    };
    
    this.notifications.unshift(notification);
    this.saveNotifications();
    this.updateNotificationBadge();
    
    // إظهار إشعار فوري للإشعارات العاجلة
    if (urgent) {
      this.showUrgentNotification(notification);
    }
  }
  
  markAsRead(notificationId) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.saveNotifications();
      this.updateNotificationBadge();
    }
  }
  
  updateNotificationBadge() {
    const unreadCount = this.notifications.filter(n => !n.read).length;
    const badge = document.getElementById('notification-badge');
    if (badge) {
      badge.textContent = unreadCount;
      badge.style.display = unreadCount > 0 ? 'block' : 'none';
    }
  }
}
```

### 2. **نظام النسخ الاحتياطي**
```javascript
function createBackup() {
  const backupData = {
    timestamp: new Date().toISOString(),
    users: localStorage.getItem('vela_users'),
    orders: localStorage.getItem('vela_orders'),
    products: localStorage.getItem('products_data'),
    subscribers: localStorage.getItem('newsletter_subscribers'),
    logs: localStorage.getItem('admin_logs')
  };
  
  const blob = new Blob([JSON.stringify(backupData, null, 2)], 
    { type: 'application/json' });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
  link.click();
  
  showToast('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

function restoreBackup(file) {
  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const backupData = JSON.parse(e.target.result);
      
      // استعادة البيانات
      if (backupData.users) localStorage.setItem('vela_users', backupData.users);
      if (backupData.orders) localStorage.setItem('vela_orders', backupData.orders);
      if (backupData.products) localStorage.setItem('products_data', backupData.products);
      if (backupData.subscribers) localStorage.setItem('newsletter_subscribers', backupData.subscribers);
      if (backupData.logs) localStorage.setItem('admin_logs', backupData.logs);
      
      // إعادة تحميل البيانات
      location.reload();
      
    } catch (error) {
      showToast('خطأ في استعادة النسخة الاحتياطية', 'error');
    }
  };
  reader.readAsText(file);
}
```

## 🚀 التطبيق والاستخدام

### 1. **HTML الأساسي للوحة الإدارة**
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>لوحة الإدارة</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
  <!-- Header -->
  <header class="admin-header">
    <div class="admin-nav">
      <h1>لوحة الإدارة</h1>
      <div class="admin-controls">
        <button class="notification-btn" onclick="showNotifications()">
          <i class="fas fa-bell"></i>
          <span class="notification-badge" id="notification-badge">0</span>
        </button>
        <button class="backup-btn" onclick="createBackup()">
          <i class="fas fa-download"></i>
          نسخ احتياطي
        </button>
        <button class="logout-btn" onclick="logout()">
          <i class="fas fa-sign-out-alt"></i>
          تسجيل الخروج
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="admin-main">
    <!-- Dashboard Stats -->
    <section class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-info">
          <h3>المستخدمين</h3>
          <p class="stat-value" id="total-users">0</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="stat-info">
          <h3>الطلبات</h3>
          <p class="stat-value" id="total-orders">0</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="stat-info">
          <h3>الإيرادات</h3>
          <p class="stat-value" id="total-revenue">0 د.ع</p>
        </div>
      </div>
    </section>

    <!-- Tabs Navigation -->
    <div class="admin-tabs">
      <button class="tab-btn active" data-tab="dashboard">
        <i class="fas fa-tachometer-alt"></i>
        لوحة التحكم
      </button>
      <button class="tab-btn" data-tab="products">
        <i class="fas fa-box"></i>
        المنتجات
      </button>
      <button class="tab-btn" data-tab="orders">
        <i class="fas fa-shopping-bag"></i>
        الطلبات
      </button>
      <button class="tab-btn" data-tab="users">
        <i class="fas fa-users"></i>
        المستخدمين
      </button>
      <button class="tab-btn" data-tab="newsletter">
        <i class="fas fa-envelope"></i>
        النشرة البريدية
      </button>
      <button class="tab-btn" data-tab="logs">
        <i class="fas fa-clipboard-list"></i>
        السجلات
      </button>
    </div>

    <!-- Tab Panels -->
    <div class="tab-content">
      <!-- Dashboard Panel -->
      <div class="tab-panel active" id="dashboard-panel">
        <h2>لوحة التحكم الرئيسية</h2>
        <!-- محتوى لوحة التحكم -->
      </div>
      
      <!-- Products Panel -->
      <div class="tab-panel" id="products-panel">
        <div class="panel-header">
          <h2>إدارة المنتجات</h2>
          <button class="btn btn-primary" onclick="showAddProductModal()">
            <i class="fas fa-plus"></i>
            إضافة منتج
          </button>
        </div>
        <div id="products-grid" class="products-grid">
          <!-- المنتجات ستظهر هنا -->
        </div>
      </div>
      
      <!-- باقي التبويبات... -->
    </div>
  </main>

  <script src="assets/js/admin-system.js"></script>
</body>
</html>
```

## 📞 الخلاصة

هذا الدليل يوفر نظام إدارة شامل يتضمن:

- ✅ **إدارة المنتجات** الكاملة مع الصور والأسعار
- ✅ **إدارة الطلبات** مع التتبع والإحصائيات
- ✅ **إدارة المستخدمين** مع نظام الرتب
- ✅ **النشرة البريدية** مع التصدير والاستيراد
- ✅ **سجل الأنشطة** المفصل لكل العمليات
- ✅ **الإحصائيات المتقدمة** والتقارير
- ✅ **نظام النسخ الاحتياطي** والاستعادة
- ✅ **الأمان والصلاحيات** المتقدمة

يمكن تطبيق هذا النظام على أي مشروع تجاري إلكتروني بسهولة! 
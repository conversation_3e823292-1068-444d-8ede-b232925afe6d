/* <PERSON><PERSON><PERSON>weets Admin Panel CSS */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
    direction: rtl;
}

[data-theme="dark"] body {
    color: #f9fafb;
    background-color: #111827;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Flexbox Utilities */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-reverse > * + * { margin-left: 0; margin-right: 1rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Spacing */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.m-4 { margin: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }

/* Colors */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }
.bg-primary { background-color: #f0a030; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #16a34a; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }
.bg-yellow-500 { background-color: #eab308; }
.bg-purple-500 { background-color: #8b5cf6; }

.text-white { color: #ffffff; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-primary { color: #f0a030; }

/* Dark mode colors */
[data-theme="dark"] .bg-white { background-color: #1f2937; }
[data-theme="dark"] .bg-gray-50 { background-color: #374151; }
[data-theme="dark"] .bg-gray-100 { background-color: #4b5563; }
[data-theme="dark"] .bg-gray-800 { background-color: #111827; }
[data-theme="dark"] .text-gray-800 { color: #f9fafb; }
[data-theme="dark"] .text-gray-700 { color: #e5e7eb; }
[data-theme="dark"] .text-gray-600 { color: #d1d5db; }

/* Typography */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }

/* Borders and Radius */
.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-700 { border-color: #374151; }
.rounded { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }

/* Width and Height */
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-full { width: 100%; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-full { height: 100%; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* Z-index */
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-60 { z-index: 60; }

/* Display */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Cursor */
.cursor-pointer { cursor: pointer; }

/* Transitions */
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }
.duration-300 { transition-duration: 300ms; }

/* Transform */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }

/* Hover Effects */
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-green-600:hover { background-color: #16a34a; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-red-600:hover { background-color: #dc2626; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }
.hover\:text-primary:hover { color: #f0a030; }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1); }

/* Focus */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(240, 160, 48, 0.5); }
.focus\:border-primary:focus { border-color: #f0a030; }

/* Responsive */
@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:flex-row { flex-direction: row; }
    .md\:w-auto { width: auto; }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\:flex-row { flex-direction: row; }
    .lg\:items-center { align-items: center; }
    .lg\:space-y-0 > * + * { margin-top: 0; }
}

/* Admin Specific Styles */
.admin-header {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    border-bottom: 1px solid #e5e7eb;
}

[data-theme="dark"] .admin-header {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-bottom-color: #374151;
}

.admin-logo {
    background: linear-gradient(135deg, #f0a030, #eab308);
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.3);
}

.admin-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(240, 160, 48, 0.4);
}

.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .stats-card {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
}

.stats-card.blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.stats-card.green {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.stats-card.yellow {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.stats-card.purple {
    background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
}

[data-theme="dark"] .stats-card.blue {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

[data-theme="dark"] .stats-card.green {
    background: linear-gradient(135deg, #14532d 0%, #166534 100%);
}

[data-theme="dark"] .stats-card.yellow {
    background: linear-gradient(135deg, #92400e 0%, #a16207 100%);
}

[data-theme="dark"] .stats-card.purple {
    background: linear-gradient(135deg, #581c87 0%, #6b21a8 100%);
}

.admin-tab {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.admin-tab.active {
    background: linear-gradient(135deg, #f0a030, #eab308);
    color: white;
    box-shadow: 0 8px 25px rgba(240, 160, 48, 0.3);
    transform: scale(1.05);
}

.admin-tab:not(.active) {
    background: transparent;
    color: #6b7280;
}

.admin-tab:not(.active):hover {
    background-color: #f3f4f6;
    color: #374151;
}

[data-theme="dark"] .admin-tab:not(.active) {
    color: #9ca3af;
}

[data-theme="dark"] .admin-tab:not(.active):hover {
    background-color: #374151;
    color: #e5e7eb;
}

.table-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

[data-theme="dark"] .table-container {
    background: #1f2937;
    border-color: #374151;
}

.table-header {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border-bottom: 1px solid #e5e7eb;
}

[data-theme="dark"] .table-header {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border-bottom-color: #4b5563;
}

.table-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f3f4f6;
}

.table-row:hover {
    background: linear-gradient(90deg, rgba(240, 160, 48, 0.05), rgba(240, 160, 48, 0.1));
    transform: scale(1.01);
}

[data-theme="dark"] .table-row {
    border-bottom-color: #374151;
}

[data-theme="dark"] .table-row:hover {
    background: linear-gradient(90deg, rgba(240, 160, 48, 0.1), rgba(240, 160, 48, 0.15));
}

.action-button {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.action-button:hover {
    transform: scale(1.2);
    filter: brightness(1.1);
}

.btn-primary {
    background: linear-gradient(135deg, #f0a030, #eab308);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(240, 160, 48, 0.3);
}

.btn-primary:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(240, 160, 48, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: scale(1.05);
    background: linear-gradient(135deg, #4b5563, #374151);
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    background: white;
    color: #1f2937;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #f0a030;
    box-shadow: 0 0 0 3px rgba(240, 160, 48, 0.1);
}

[data-theme="dark"] .form-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.notification-badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    width: 1.25rem;
    height: 1.25rem;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Additional Utilities */
.min-w-full { min-width: 100%; }
.divide-y > * + * { border-top-width: 1px; }
.divide-gray-200 > * + * { border-top-color: #e5e7eb; }
.whitespace-nowrap { white-space: nowrap; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }
.leading-tight { line-height: 1.25; }

/* Table Styles */
table {
    border-collapse: collapse;
    width: 100%;
}

th, td {
    padding: 1rem 1.5rem;
    text-align: right;
    border-bottom: 1px solid #f3f4f6;
}

th {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    font-weight: 600;
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

[data-theme="dark"] th {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: #9ca3af;
    border-bottom-color: #4b5563;
}

[data-theme="dark"] td {
    border-bottom-color: #374151;
}

tr:hover {
    background: linear-gradient(90deg, rgba(240, 160, 48, 0.05), rgba(240, 160, 48, 0.1));
}

[data-theme="dark"] tr:hover {
    background: linear-gradient(90deg, rgba(240, 160, 48, 0.1), rgba(240, 160, 48, 0.15));
}

/* Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-green {
    background-color: #dcfce7;
    color: #166534;
}

.badge-yellow {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-red {
    background-color: #fee2e2;
    color: #991b1b;
}

[data-theme="dark"] .badge-green {
    background-color: #14532d;
    color: #bbf7d0;
}

[data-theme="dark"] .badge-yellow {
    background-color: #92400e;
    color: #fde68a;
}

[data-theme="dark"] .badge-red {
    background-color: #991b1b;
    color: #fecaca;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #f0a030;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

[data-theme="dark"] .modal-content {
    background: #1f2937;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }

    .stats-card {
        padding: 1rem;
    }

    .admin-tab {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .grid-cols-3 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .flex-col {
        flex-direction: column;
    }

    .space-y-2 > * + * {
        margin-top: 0.5rem;
    }

    th, td {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .w-64 {
        width: 100%;
    }
}

/**
 * Main JavaScript file
 * Handles general functionality like mobile menu, smooth scrolling, theme switching
 */

class MainController {
    constructor() {
        this.initElements();
        this.initEventListeners();
        this.initTheme();
        this.initSmoothScrolling();
        this.initLanguageSwitcher();
    }

    initElements() {
        this.menuToggle = document.getElementById('menu-toggle');
        this.mobileMenu = document.getElementById('mobile-menu');
        this.closeMenu = document.getElementById('close-menu');
        this.overlay = document.getElementById('overlay');
        this.themeToggle = document.getElementById('theme-toggle');
        // this.loginToggle = document.getElementById('login-toggle'); // Handled by auth-system.js
    }

    initEventListeners() {
        // Mobile menu
        this.menuToggle?.addEventListener('click', () => this.openMobileMenu());
        this.closeMenu?.addEventListener('click', () => this.closeMobileMenu());

        // Theme toggle
        this.themeToggle?.addEventListener('click', () => this.toggleTheme());

        // Login toggle is now handled by auth-system.js
        // this.loginToggle?.addEventListener('click', () => this.handleLogin());

        // Language switcher
        this.initLanguageSwitcher();

        // Close mobile menu when clicking on menu items
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', () => this.closeMobileMenu());
        });

        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());

        // Scroll handler for navbar
        window.addEventListener('scroll', () => this.handleScroll());

        // Contact form handler
        this.initContactForm();
    }

    initTheme() {
        // Get saved theme from localStorage or detect system preference
        let savedTheme = localStorage.getItem('theme');

        if (!savedTheme) {
            // Detect system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                savedTheme = 'dark';
            } else {
                savedTheme = 'light';
            }
        }

        this.setTheme(savedTheme);

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }

        console.log('Theme initialized:', savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);

        console.log('Theme toggled to:', newTheme);
    }

    setTheme(theme) {
        // Set theme on body and html elements
        document.body.setAttribute('data-theme', theme);

        // Add/remove dark class for Tailwind CSS
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        // Save theme preference
        localStorage.setItem('theme', theme);

        // Update theme toggle button appearance
        this.updateThemeToggleButton(theme);

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    updateThemeToggleButton(theme) {
        if (!this.themeToggle) return;

        const sunIcon = this.themeToggle.querySelector('.theme-light');
        const moonIcon = this.themeToggle.querySelector('.theme-dark');

        if (theme === 'dark') {
            sunIcon?.classList.add('hidden');
            moonIcon?.classList.remove('hidden');
            this.themeToggle.setAttribute('aria-label', 'تبديل إلى المظهر الفاتح');
        } else {
            sunIcon?.classList.remove('hidden');
            moonIcon?.classList.add('hidden');
            this.themeToggle.setAttribute('aria-label', 'تبديل إلى المظهر الداكن');
        }
    }

    openMobileMenu() {
        this.mobileMenu?.classList.remove('translate-x-full');
        this.overlay?.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    closeMobileMenu() {
        this.mobileMenu?.classList.add('translate-x-full');
        this.overlay?.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    initSmoothScrolling() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                
                const href = anchor.getAttribute('href');
                if (href === '#') return;

                const target = document.querySelector(href);
                if (target) {
                    const headerHeight = document.querySelector('header')?.offsetHeight || 80;
                    const targetPosition = target.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    initLanguageSwitcher() {
        const languageToggle = document.getElementById('language-toggle');
        const languageDropdown = document.getElementById('language-dropdown');
        let isDropdownOpen = false;
        let dropdownTimeout;

        if (!languageToggle || !languageDropdown) return;

        // Toggle dropdown on click
        languageToggle.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (isDropdownOpen) {
                this.closeLanguageDropdown();
            } else {
                this.openLanguageDropdown();
            }
        });

        // Keep dropdown open when hovering over it
        languageDropdown.addEventListener('mouseenter', () => {
            clearTimeout(dropdownTimeout);
        });

        // Close dropdown with delay when leaving
        languageDropdown.addEventListener('mouseleave', () => {
            dropdownTimeout = setTimeout(() => {
                this.closeLanguageDropdown();
            }, 300);
        });

        // Language selection
        document.querySelectorAll('[data-lang]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = e.target.getAttribute('data-lang');
                this.switchLanguage(lang);
                this.closeLanguageDropdown();
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                this.closeLanguageDropdown();
            }
        });
    }

    openLanguageDropdown() {
        const languageDropdown = document.getElementById('language-dropdown');
        if (languageDropdown) {
            languageDropdown.classList.remove('hidden');
            this.isDropdownOpen = true;
        }
    }

    closeLanguageDropdown() {
        const languageDropdown = document.getElementById('language-dropdown');
        if (languageDropdown) {
            languageDropdown.classList.add('hidden');
            this.isDropdownOpen = false;
        }
    }

    switchLanguage(lang) {
        // Store selected language
        localStorage.setItem('selectedLanguage', lang);
        
        // For now, just show a notification
        // In a real implementation, this would reload the page with different content
        this.showNotification(`تم تغيير اللغة إلى ${this.getLanguageName(lang)}`, 'info');
    }

    getLanguageName(lang) {
        const languages = {
            'ar': 'العربية',
            'en': 'English', 
            'ku': 'کوردی'
        };
        return languages[lang] || lang;
    }

    handleLogin() {
        // Login functionality is now handled by auth-system.js
        if (window.authManager) {
            if (window.authManager.isAuthenticated()) {
                window.authManager.openAccountModal();
            } else {
                window.authManager.openAuthModal();
            }
        }
    }

    handleResize() {
        // Close mobile menu on resize to desktop
        if (window.innerWidth >= 1024) {
            this.closeMobileMenu();
        }
    }

    handleScroll() {
        const header = document.querySelector('header');
        if (!header) return;

        if (window.scrollY > 100) {
            header.classList.add('shadow-lg');
        } else {
            header.classList.remove('shadow-lg');
        }
    }

    initContactForm() {
        const contactForm = document.querySelector('#contact form');
        if (!contactForm) return;

        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleContactFormSubmission(contactForm);
        });
    }

    handleContactFormSubmission(form) {
        const formData = new FormData(form);
        const data = {
            name: formData.get('name') || document.getElementById('name')?.value,
            email: formData.get('email') || document.getElementById('email')?.value,
            subject: formData.get('subject') || document.getElementById('subject')?.value,
            message: formData.get('message') || document.getElementById('message')?.value
        };

        // Validate form
        if (!data.name || !data.email || !data.message) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = `
            <div class="loading-spinner inline-block ml-2"></div>
            جاري الإرسال...
        `;
        submitBtn.disabled = true;

        // Simulate form submission
        setTimeout(() => {
            // Save to localStorage (in real app, send to server)
            this.saveContactMessage(data);
            
            // Show success message
            this.showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً', 'success');
            
            // Reset form
            form.reset();
            
            // Restore button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }

    saveContactMessage(data) {
        const messages = JSON.parse(localStorage.getItem('contactMessages')) || [];
        const message = {
            id: 'MSG-' + Date.now(),
            ...data,
            timestamp: new Date().toISOString()
        };
        
        messages.push(message);
        localStorage.setItem('contactMessages', JSON.stringify(messages));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 notification fade-in transition-colors duration-300`;

        let bgColor = 'bg-blue-500 dark:bg-blue-600';
        let icon = 'ri-information-line';

        if (type === 'error') {
            bgColor = 'bg-red-500 dark:bg-red-600';
            icon = 'ri-error-warning-line';
        } else if (type === 'success') {
            bgColor = 'bg-green-500 dark:bg-green-600';
            icon = 'ri-check-line';
        } else if (type === 'warning') {
            bgColor = 'bg-yellow-500 dark:bg-yellow-600';
            icon = 'ri-alert-line';
        }

        notification.className += ` ${bgColor} text-white`;

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="${icon} ri-lg ml-2"></i>
                <span>${message}</span>
                <button class="mr-4 hover:opacity-75 transition-opacity" onclick="this.parentElement.parentElement.remove()">
                    <i class="ri-close-line"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Animation observer for fade-in effects
    initAnimationObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            });

            // Observe elements that should animate in
            document.querySelectorAll('.product-card, .about-card').forEach(el => {
                observer.observe(el);
            });
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mainController = new MainController();
    
    // Initialize animation observer after a short delay
    setTimeout(() => {
        window.mainController.initAnimationObserver();
    }, 100);
});

// Export for global access
window.MainController = MainController; 
/**
 * Checkout System
 * Handles checkout form, payment methods, and order completion
 */

class CheckoutManager {
    constructor() {
        this.initElements();
        this.initEventListeners();
        this.setupPaymentMethods();
    }

    initElements() {
        this.checkoutBtn = document.getElementById('checkout-btn');
        this.checkoutModal = document.getElementById('checkout-modal');
        this.closeCheckoutBtn = document.getElementById('close-checkout');
        this.checkoutForm = document.getElementById('checkout-form');
        this.successModal = document.getElementById('success-modal');
        this.closeSuccessBtn = document.getElementById('close-success');
        this.overlay = document.getElementById('overlay');
    }

    initEventListeners() {
        try {
            // Open checkout modal
            this.checkoutBtn?.addEventListener('click', () => this.openCheckout());

            // Close checkout modal
            this.closeCheckoutBtn?.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeCheckout();
            });

            // Close on overlay click
            this.overlay?.addEventListener('click', (e) => {
                if (e.target === this.overlay) {
                    this.closeCheckout();
                }
            });

            // Close on ESC key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.checkoutModal && !this.checkoutModal.classList.contains('hidden')) {
                    this.closeCheckout();
                }
            });

            // Handle form submission
            this.checkoutForm?.addEventListener('submit', (e) => this.handleFormSubmission(e));

            // Close success modal
            this.closeSuccessBtn?.addEventListener('click', () => this.closeSuccessModal());

            // Form validation listeners
            this.setupFormValidation();
        } catch (error) {
            console.error('خطأ في تهيئة أحداث الطلبات:', error);
        }
    }

    setupPaymentMethods() {
        // Payment method is now fixed to cash on delivery
        // No need for radio button logic since there's only one option
        console.log('Payment method set to: Cash on Delivery');
    }

    setupFormValidation() {
        const requiredFields = [
            'customer-name',
            'customer-phone', 
            'customer-province',
            'customer-address'
        ];

        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => this.validateField(field));
                field.addEventListener('input', () => this.clearFieldError(field));
            }
        });

        // Phone number validation
        const phoneField = document.getElementById('customer-phone');
        if (phoneField) {
            phoneField.addEventListener('input', (e) => {
                // Only allow numbers
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
            });
        }
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Remove existing error
        this.clearFieldError(field);

        // Validate based on field type
        switch (field.id) {
            case 'customer-name':
                if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'يجب أن يكون الاسم أكثر من حرفين';
                }
                break;
            
            case 'customer-phone':
                if (!/^(07[3-9][0-9]{8}|75[0-9]{8})$/.test(value)) {
                    isValid = false;
                    errorMessage = 'رقم الهاتف غير صحيح (مثال: 07901234567)';
                }
                break;
            
            case 'customer-province':
                if (!value) {
                    isValid = false;
                    errorMessage = 'يجب اختيار المحافظة';
                }
                break;
            
            case 'customer-address':
                if (value.length < 10) {
                    isValid = false;
                    errorMessage = 'يجب إدخال عنوان تفصيلي أكثر وضوحاً';
                }
                break;
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    showFieldError(field, message) {
        field.classList.add('border-red-500');
        
        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'text-red-500 text-sm mt-1 field-error';
        errorElement.textContent = message;
        
        // Insert after field
        field.parentNode.insertBefore(errorElement, field.nextSibling);
    }

    clearFieldError(field) {
        field.classList.remove('border-red-500');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    openCheckout() {
        if (!window.cartManager || window.cartManager.getCart().length === 0) {
            if (window.customModal) {
                window.customModal.error('السلة فارغة! يرجى إضافة منتجات قبل إتمام الطلب.');
            }
            return;
        }

        // التحقق من تسجيل الدخول
        if (window.authManager && window.authManager.isAuthenticated()) {
            // المستخدم مسجل الدخول - ملء البيانات تلقائياً
            this.prefillUserData();
        }

        // Close cart sidebar
        document.getElementById('cart-sidebar')?.classList.remove('open');

        // Open checkout modal
        this.checkoutModal?.classList.remove('hidden');
        this.overlay?.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Focus on first field
        const firstField = document.getElementById('customer-name');
        if (firstField) {
            setTimeout(() => firstField.focus(), 100);
        }
    }

    prefillUserData() {
        if (!window.authManager) return;

        const user = window.authManager.getCurrentUser();
        if (!user) return;

        // ملء البيانات من الحساب المسجل
        const nameField = document.getElementById('customer-name');
        const phoneField = document.getElementById('customer-phone');
        const provinceField = document.getElementById('customer-province');

        if (nameField) nameField.value = user.name || '';
        if (phoneField) phoneField.value = user.phone || '';
        if (provinceField) provinceField.value = user.province || '';

        // إضافة ملاحظة للمستخدم
        const existingNote = document.querySelector('.user-prefill-note');
        if (!existingNote && nameField) {
            const note = document.createElement('div');
            note.className = 'user-prefill-note text-sm text-green-600 dark:text-green-400 mt-2 flex items-center';
            note.innerHTML = `
                <i class="ri-check-line ml-1"></i>
                تم ملء البيانات من حسابك المسجل
            `;
            nameField.parentNode.appendChild(note);
        }
    }

    closeCheckout() {
        this.checkoutModal?.classList.add('hidden');
        this.overlay?.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    handleFormSubmission(e) {
        e.preventDefault();
        
        // Validate all fields
        const requiredFields = [
            'customer-name',
            'customer-phone',
            'customer-province', 
            'customer-address'
        ];

        let isFormValid = true;
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && !this.validateField(field)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            // Use custom modal for error message
            if (window.customModal) {
                window.customModal.error(
                    'يرجى تصحيح الأخطاء في النموذج والتأكد من ملء جميع الحقول المطلوبة بشكل صحيح.',
                    'خطأ في النموذج'
                );
            } else {
                this.showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
            }
            return;
        }

        // Get form data
        const formData = this.getFormData();
        
        // Process order
        this.processOrder(formData);
    }

    getFormData() {
        return {
            customerName: document.getElementById('customer-name')?.value.trim(),
            customerPhone: document.getElementById('customer-phone')?.value.trim(),
            customerProvince: document.getElementById('customer-province')?.value,
            customerAddress: document.getElementById('customer-address')?.value.trim(),
            paymentMethod: 'cash', // Fixed to cash on delivery only
            cart: window.cartManager?.getCart() || [],
            totalAmount: window.cartManager?.getTotalPrice() || 0,
            totalItems: window.cartManager?.getTotalItems() || 0,
            orderDate: new Date().toISOString()
        };
    }

    processOrder(orderData) {
        // Show loading state
        this.showLoadingState();
        
        // Simulate order processing
        setTimeout(() => {
            // Save order to localStorage (in real app, send to server)
            this.saveOrder(orderData);
            
            // Clear cart
            window.cartManager?.clearCart();
            
            // Show success
            this.showOrderSuccess();
            
            // Hide loading state
            this.hideLoadingState();
        }, 2000);
    }

    saveOrder(orderData) {
        const orders = JSON.parse(localStorage.getItem('orders')) || [];
        const orderId = 'ORDER-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // إضافة معلومات المستخدم المسجل إن وجد
        let userId = null;
        if (window.authManager && window.authManager.isAuthenticated()) {
            const user = window.authManager.getCurrentUser();
            userId = user?.userId;
        }

        const order = {
            id: orderId,
            userId: userId, // ربط الطلب بالمستخدم
            ...orderData,
            status: 'pending',
            createdAt: new Date().toISOString()
        };

        orders.push(order);
        localStorage.setItem('orders', JSON.stringify(orders));

        // Store last order for reference
        localStorage.setItem('lastOrder', JSON.stringify(order));

        // تحديث عدد الطلبات المكتملة للمستخدم المسجل
        if (userId && window.authManager) {
            this.updateUserOrderCount(userId);
        }
    }

    async updateUserOrderCount(userId) {
        try {
            const users = await window.authManager.getUsers();
            const userIndex = users.findIndex(user => user.userId === userId);

            if (userIndex !== -1) {
                users[userIndex].completedOrders = (users[userIndex].completedOrders || 0) + 1;

                // تحديث الرتبة إذا لزم الأمر
                if (users[userIndex].completedOrders >= 5 && users[userIndex].rank === 'normal') {
                    users[userIndex].rank = 'premium';
                }

                localStorage.setItem(window.authManager.usersKey, JSON.stringify(users));

                // تحديث المستخدم الحالي في الجلسة
                if (window.authManager.currentUser && window.authManager.currentUser.userId === userId) {
                    window.authManager.currentUser.completedOrders = users[userIndex].completedOrders;
                    window.authManager.currentUser.rank = users[userIndex].rank;
                    localStorage.setItem(window.authManager.userKey, JSON.stringify(window.authManager.currentUser));
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث عدد طلبات المستخدم:', error);
        }
    }

    showOrderSuccess() {
        this.checkoutModal?.classList.add('hidden');

        // Use custom modal for success message
        if (window.customModal) {
            const lastOrder = JSON.parse(localStorage.getItem('lastOrder'));
            const orderId = lastOrder ? lastOrder.id : 'غير محدد';
            const totalAmount = lastOrder ? lastOrder.totalAmount.toLocaleString() : '0';

            window.customModal.success(
                `تم إرسال طلبك بنجاح! \n\nرقم الطلب: ${orderId}\nالمبلغ الإجمالي: ${totalAmount} د.ع\nطريقة الدفع: نقداً عند الاستلام\n\nسيتم التواصل معك قريباً لتأكيد الطلب وترتيب موعد التوصيل.`,
                'تم إرسال الطلب بنجاح!'
            );

            // Close checkout modal and reset after user closes success modal
            setTimeout(() => {
                this.overlay?.classList.add('hidden');
                document.body.style.overflow = 'auto';

                // Reload page to reset UI
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }, 3000);
        } else {
            // Fallback to existing success modal
            this.successModal?.classList.remove('hidden');
        }
    }

    closeSuccessModal() {
        this.successModal?.classList.add('hidden');
        this.overlay?.classList.add('hidden');
        document.body.style.overflow = 'auto';

        // Reload page to reset UI
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    showLoadingState() {
        const submitBtn = this.checkoutForm?.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = `
                <div class="loading-spinner mx-auto"></div>
                <span class="mr-2">جاري المعالجة...</span>
            `;
        }
    }

    hideLoadingState() {
        const submitBtn = this.checkoutForm?.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'تأكيد الطلب';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 notification-${type}`;
        
        if (type === 'error') {
            notification.className += ' bg-red-500 text-white';
        } else if (type === 'success') {
            notification.className += ' bg-green-500 text-white';
        } else {
            notification.className += ' bg-blue-500 text-white';
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="ri-information-line ri-lg ml-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize checkout when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.checkoutManager = new CheckoutManager();
        console.log('✅ تم تهيئة نظام الطلبات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام الطلبات:', error);
    }
});
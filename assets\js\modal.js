/**
 * Custom Modal System
 * Replaces browser alerts, confirms, and prompts with custom styled modals
 */

class CustomModal {
    constructor() {
        this.modal = document.getElementById('custom-modal');
        this.modalContent = document.getElementById('modal-content');
        this.modalIcon = document.getElementById('modal-icon');
        this.modalTitle = document.getElementById('modal-title');
        this.modalMessage = document.getElementById('modal-message');
        this.modalCancel = document.getElementById('modal-cancel');
        this.modalConfirm = document.getElementById('modal-confirm');
        
        this.initEventListeners();
    }

    initEventListeners() {
        // Close modal when clicking outside
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // Cancel button
        this.modalCancel.addEventListener('click', () => {
            this.close();
            if (this.onCancel) this.onCancel();
        });

        // Confirm button
        this.modalConfirm.addEventListener('click', () => {
            this.close();
            if (this.onConfirm) this.onConfirm();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.close();
                if (this.onCancel) this.onCancel();
            }
        });
    }

    show(options = {}) {
        const {
            type = 'confirm',
            title = 'تأكيد',
            message = 'هل أنت متأكد؟',
            confirmText = 'تأكيد',
            cancelText = 'إلغاء',
            confirmColor = 'red',
            onConfirm = null,
            onCancel = null
        } = options;

        // Set callbacks
        this.onConfirm = onConfirm;
        this.onCancel = onCancel;

        // Set icon based on type
        this.setIcon(type);

        // Set content
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.modalCancel.textContent = cancelText;
        this.modalConfirm.textContent = confirmText;

        // Set confirm button color
        this.setConfirmButtonColor(confirmColor);

        // Show modal with animation
        this.modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Trigger animation
        setTimeout(() => {
            this.modalContent.style.transform = 'scale(1)';
            this.modalContent.style.opacity = '1';
        }, 10);
    }

    setIcon(type) {
        let iconHTML = '';
        let bgColor = '';

        switch (type) {
            case 'warning':
                iconHTML = '<i class="ri-alert-line ri-2x text-orange-500"></i>';
                bgColor = 'bg-orange-100 dark:bg-orange-900';
                break;
            case 'danger':
                iconHTML = '<i class="ri-error-warning-line ri-2x text-red-500"></i>';
                bgColor = 'bg-red-100 dark:bg-red-900';
                break;
            case 'success':
                iconHTML = '<i class="ri-check-line ri-2x text-green-500"></i>';
                bgColor = 'bg-green-100 dark:bg-green-900';
                break;
            case 'info':
                iconHTML = '<i class="ri-information-line ri-2x text-blue-500"></i>';
                bgColor = 'bg-blue-100 dark:bg-blue-900';
                break;
            case 'delete':
                iconHTML = '<i class="ri-delete-bin-line ri-2x text-red-500"></i>';
                bgColor = 'bg-red-100 dark:bg-red-900';
                break;
            default: // confirm
                iconHTML = '<i class="ri-question-line ri-2x text-primary"></i>';
                bgColor = 'bg-yellow-100 dark:bg-yellow-900';
        }

        this.modalIcon.innerHTML = iconHTML;
        this.modalIcon.className = `w-12 h-12 rounded-full flex items-center justify-center ml-4 ${bgColor}`;
    }

    setConfirmButtonColor(color) {
        // Remove existing color classes
        this.modalConfirm.className = this.modalConfirm.className.replace(/bg-\w+-\d+/g, '');
        this.modalConfirm.className = this.modalConfirm.className.replace(/hover:bg-\w+-\d+/g, '');

        let colorClasses = '';
        switch (color) {
            case 'red':
                colorClasses = 'bg-red-500 hover:bg-red-600';
                break;
            case 'green':
                colorClasses = 'bg-green-500 hover:bg-green-600';
                break;
            case 'blue':
                colorClasses = 'bg-blue-500 hover:bg-blue-600';
                break;
            case 'primary':
                colorClasses = 'bg-primary hover:bg-opacity-90';
                break;
            default:
                colorClasses = 'bg-red-500 hover:bg-red-600';
        }

        this.modalConfirm.className += ` ${colorClasses}`;
    }

    close() {
        // Animate out
        this.modalContent.style.transform = 'scale(0.95)';
        this.modalContent.style.opacity = '0';

        setTimeout(() => {
            this.modal.classList.add('hidden');
            document.body.style.overflow = '';
        }, 300);
    }

    // Helper methods for common use cases
    confirm(message, onConfirm, onCancel = null) {
        this.show({
            type: 'confirm',
            title: 'تأكيد',
            message: message,
            confirmText: 'نعم',
            cancelText: 'إلغاء',
            confirmColor: 'primary',
            onConfirm: onConfirm,
            onCancel: onCancel
        });
    }

    warning(message, onConfirm, onCancel = null) {
        this.show({
            type: 'warning',
            title: 'تحذير',
            message: message,
            confirmText: 'متابعة',
            cancelText: 'إلغاء',
            confirmColor: 'red',
            onConfirm: onConfirm,
            onCancel: onCancel
        });
    }

    deleteConfirm(message, onConfirm, onCancel = null) {
        this.show({
            type: 'delete',
            title: 'تأكيد الحذف',
            message: message,
            confirmText: 'نعم، احذف',
            cancelText: 'إلغاء',
            confirmColor: 'red',
            onConfirm: onConfirm,
            onCancel: onCancel
        });
    }

    alert(message, title = 'تنبيه', type = 'info') {
        this.show({
            type: type,
            title: title,
            message: message,
            confirmText: 'حسناً',
            cancelText: '',
            confirmColor: 'primary',
            onConfirm: () => {},
            onCancel: null
        });

        // Hide cancel button for alerts
        this.modalCancel.style.display = 'none';
    }

    success(message, title = 'نجح') {
        this.alert(message, title, 'success');
    }

    error(message, title = 'خطأ') {
        this.alert(message, title, 'danger');
    }
}

// Initialize custom modal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.customModal = new CustomModal();
});

// Override browser confirm function (optional)
// window.confirm = (message) => {
//     return new Promise((resolve) => {
//         window.customModal.confirm(message, () => resolve(true), () => resolve(false));
//     });
// };

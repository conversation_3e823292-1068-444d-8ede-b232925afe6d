/**
 * VelaSweets Authentication System
 * نظام المصادقة الشامل لمتجر ڤيلا سويتز
 */

class AuthManager {
  constructor() {
    this.usersKey = 'velasweets_users';
    this.sessionKey = 'velasweets_session';
    this.userKey = 'velasweets_current_user';
    this.deletedAccountsKey = 'velasweets_deleted_accounts';
    this.currentUser = null;
    this.init();
  }

  // تهيئة النظام
  init() {
    // إنشاء حسابات المديرين الافتراضية
    this.createDefaultAdminAccounts();

    if (this.isAuthenticated()) {
      this.currentUser = this.getCurrentUser();
      console.log('🔐 تم استعادة الجلسة للمستخدم:', this.currentUser?.name);
      this.updateUIForLoggedInUser();
    }
  }

  // إنشاء حسابات المديرين الافتراضية
  async createDefaultAdminAccounts() {
    try {
      const users = await this.getUsers();
      const adminEmails = ['<EMAIL>', '<EMAIL>'];

      for (const email of adminEmails) {
        const existingAdmin = users.find(user => user.email === email);
        if (!existingAdmin) {
          const adminData = {
            name: email === '<EMAIL>' ? 'مدير النظام' : 'مدير المتجر',
            email: email,
            phone: email === '<EMAIL>' ? '***********' : '***********',
            province: 'بغداد',
            password: 'Admin@123' // كلمة مرور افتراضية قوية
          };

          await this.createUser(adminData);
          console.log(`✅ تم إنشاء حساب المدير: ${adminData.name}`);
        }
      }
    } catch (error) {
      console.error('خطأ في إنشاء حسابات المديرين:', error);
    }
  }

  // تسجيل مستخدم جديد
  async register(userData) {
    try {
      // 1. التحقق من البيانات المطلوبة
      this.validateRequiredFields(userData);
      
      // 2. التحقق من صحة البيانات
      this.validateUserData(userData);
      
      // 3. التحقق من عدم وجود المستخدم مسبقاً
      await this.checkUserExists(userData);
      
      // 4. إنشاء حساب المستخدم
      const user = await this.createUser(userData);
      
      // 5. إنشاء جلسة تسجيل دخول
      await this.createSession(user);
      
      return user;
    } catch (error) {
      console.error('❌ خطأ في التسجيل:', error);
      throw error;
    }
  }

  // تسجيل الدخول
  async login(identifier, password) {
    try {
      // 1. التحقق من وجود البيانات
      if (!identifier || !password) {
        throw new Error('البريد الإلكتروني/رقم الهاتف وكلمة المرور مطلوبان');
      }

      // 2. البحث عن المستخدم
      let user = null;
      if (this.isValidEmail(identifier)) {
        user = await this.getUserByEmail(identifier.toLowerCase());
      } else if (this.isValidPhone(identifier)) {
        user = await this.getUserByPhone(identifier);
      } else {
        throw new Error('يرجى إدخال بريد إلكتروني أو رقم هاتف صحيح');
      }

      if (!user) {
        throw new Error('المستخدم غير موجود');
      }

      // 3. التحقق من كلمة المرور
      const isValidPassword = await this.comparePassword(password, user.password);
      if (!isValidPassword) {
        throw new Error('كلمة المرور غير صحيحة');
      }

      // 4. إنشاء جلسة جديدة
      await this.createSession(user);
      
      return user;
    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      throw error;
    }
  }

  // التحقق من البيانات المطلوبة
  validateRequiredFields(userData) {
    const requiredFields = ['name', 'email', 'phone', 'password', 'province'];
    const missingFields = requiredFields.filter(field => !userData[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`البيانات التالية مطلوبة: ${missingFields.join(', ')}`);
    }
  }

  // التحقق من صحة البيانات
  validateUserData(userData) {
    // التحقق من البريد الإلكتروني
    if (!this.isValidEmail(userData.email)) {
      throw new Error('صيغة البريد الإلكتروني غير صحيحة');
    }

    // التحقق من رقم الهاتف
    if (!this.isValidPhone(userData.phone)) {
      throw new Error('رقم الهاتف يجب أن يبدأ بـ 07 ويتكون من 11 رقماً');
    }

    // التحقق من قوة كلمة المرور
    const passwordValidation = this.validatePasswordStrength(userData.password);
    if (!passwordValidation.isValid) {
      throw new Error('كلمة المرور ضعيفة: ' + passwordValidation.feedback.join(', '));
    }
  }

  // التحقق من عدم وجود المستخدم مسبقاً
  async checkUserExists(userData) {
    if (await this.userExists(userData.email)) {
      throw new Error('البريد الإلكتروني مسجل مسبقاً');
    }

    if (await this.phoneExists(userData.phone)) {
      throw new Error('رقم الهاتف مسجل مسبقاً');
    }
  }

  // إنشاء حساب مستخدم جديد
  async createUser(userData) {
    const userId = await this.generateUserId();
    const hashedPassword = await this.hashPassword(userData.password);

    const user = {
      userId: userId,
      name: userData.name,
      email: userData.email.toLowerCase(),
      phone: userData.phone,
      backupPhone: userData.backupPhone || null,
      province: userData.province,
      password: hashedPassword,
      addresses: [],
      completedOrders: 0,
      rank: 'normal',
      createdAt: new Date().toISOString(),
      isActive: true
    };

    await this.saveUser(user);
    return user;
  }

  // تشفير كلمة المرور
  async hashPassword(password) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(password + 'velasweets_salt_2024');
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('خطأ في تشفير كلمة المرور:', error);
      // احتياطي إذا لم تتوفر Web Crypto API
      return btoa(password + 'velasweets_salt_2024');
    }
  }

  // مقارنة كلمة المرور
  async comparePassword(password, hashedPassword) {
    try {
      const hashedInput = await this.hashPassword(password);
      return hashedInput === hashedPassword;
    } catch (error) {
      console.error('خطأ في مقارنة كلمة المرور:', error);
      return false;
    }
  }

  // التحقق من صحة البريد الإلكتروني
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // التحقق من صحة رقم الهاتف العراقي
  isValidPhone(phone) {
    return /^07[0-9]{9}$/.test(phone);
  }

  // التحقق من قوة كلمة المرور
  validatePasswordStrength(password) {
    const feedback = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('8 أحرف على الأقل');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('حرف كبير واحد');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('حرف صغير واحد');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('رقم واحد');

    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    else feedback.push('رمز خاص واحد');

    return {
      isValid: score >= 3, // تخفيف المتطلبات قليلاً
      score: score,
      feedback: feedback
    };
  }

  // إنشاء معرف مستخدم فريد
  async generateUserId() {
    const users = await this.getUsers();
    let nextId = 1;
    
    users.forEach(user => {
      if (user.userId && user.userId.startsWith('VS')) {
        const idNum = parseInt(user.userId.replace('VS', ''));
        if (!isNaN(idNum) && idNum >= nextId) {
          nextId = idNum + 1;
        }
      }
    });
    
    return 'VS' + nextId.toString().padStart(8, '0');
  }

  // التحقق من وجود المستخدم بالبريد الإلكتروني
  async userExists(email) {
    const users = await this.getUsers();
    return users.some(user => user.email === email.toLowerCase());
  }

  // التحقق من وجود رقم الهاتف
  async phoneExists(phone) {
    const users = await this.getUsers();
    return users.some(user => user.phone === phone);
  }

  // إدارة الجلسات
  async createSession(userData) {
    const sessionData = {
      userId: userData.userId,
      email: userData.email,
      timestamp: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 أيام
    };

    localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
    localStorage.setItem(this.userKey, JSON.stringify(userData));
    this.currentUser = userData;

    // إرسال حدث تسجيل الدخول
    window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: userData }));
    
    console.log('🔑 تم إنشاء الجلسة بنجاح');
    this.updateUIForLoggedInUser();
  }

  // التحقق من صحة الجلسة
  isAuthenticated() {
    const session = this.getSession();
    if (!session) return false;
    
    const expiresAt = new Date(session.expiresAt);
    const now = new Date();
    
    return now < expiresAt;
  }

  // الحصول على المستخدم الحالي
  getCurrentUser() {
    if (!this.isAuthenticated()) return null;
    return JSON.parse(localStorage.getItem(this.userKey));
  }

  // تسجيل الخروج
  logout() {
    localStorage.removeItem(this.sessionKey);
    localStorage.removeItem(this.userKey);
    this.currentUser = null;
    
    window.dispatchEvent(new CustomEvent('userLoggedOut'));
    console.log('🚪 تم تسجيل الخروج');
    this.updateUIForLoggedOutUser();
  }

  // إدارة البيانات
  async getUsers() {
    try {
      const users = localStorage.getItem(this.usersKey);
      return users ? JSON.parse(users) : [];
    } catch (error) {
      console.error('خطأ في استرجاع المستخدمين:', error);
      return [];
    }
  }

  async saveUser(user) {
    try {
      const users = await this.getUsers();
      users.push(user);
      localStorage.setItem(this.usersKey, JSON.stringify(users));
      console.log('💾 تم حفظ المستخدم بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ المستخدم:', error);
      throw new Error('فشل في حفظ بيانات المستخدم');
    }
  }

  async getUserByEmail(email) {
    const users = await this.getUsers();
    return users.find(user => user.email === email.toLowerCase());
  }

  async getUserByPhone(phone) {
    const users = await this.getUsers();
    return users.find(user => user.phone === phone);
  }

  getSession() {
    try {
      const session = localStorage.getItem(this.sessionKey);
      return session ? JSON.parse(session) : null;
    } catch (error) {
      console.error('خطأ في استرجاع الجلسة:', error);
      return null;
    }
  }

  // تحديث واجهة المستخدم
  updateUIForLoggedInUser() {
    const user = this.getCurrentUser();
    if (!user) return;

    // تحديث زر تسجيل الدخول
    const loginBtn = document.getElementById('login-toggle');
    if (loginBtn) {
      loginBtn.innerHTML = `
        <div class="w-8 h-8 flex items-center justify-center text-gray-700 dark:text-gray-300">
          <i class="ri-user-line ri-lg"></i>
        </div>
      `;
      loginBtn.setAttribute('aria-label', 'الملف الشخصي');
      loginBtn.onclick = () => this.openAccountModal();
    }

    // إضافة رابط لوحة الإدارة للمديرين
    this.showAdminLinkIfAdmin(user);
  }

  // فحص إذا كان المستخدم مدير وإظهار رابط الإدارة
  showAdminLinkIfAdmin(user) {
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const adminPhones = ['***********', '***********'];

    const isAdmin = adminEmails.includes(user.email?.toLowerCase()) ||
                   adminPhones.includes(user.phone);

    if (isAdmin) {
      // البحث عن مكان إضافة رابط الإدارة
      const headerActions = document.querySelector('.header-actions') ||
                           document.querySelector('header .flex.items-center.space-x-4');

      if (headerActions && !document.getElementById('admin-link')) {
        const adminLink = document.createElement('a');
        adminLink.id = 'admin-link';
        adminLink.href = 'admin.html';
        adminLink.className = 'p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors';
        adminLink.setAttribute('aria-label', 'لوحة الإدارة');
        adminLink.innerHTML = `
          <div class="w-8 h-8 flex items-center justify-center text-gray-700 dark:text-gray-300">
            <i class="ri-admin-line ri-lg"></i>
          </div>
        `;

        // إضافة الرابط قبل زر تسجيل الدخول
        const loginBtn = document.getElementById('login-toggle');
        if (loginBtn) {
          headerActions.insertBefore(adminLink, loginBtn);
        }
      }
    } else {
      // إزالة رابط الإدارة إذا لم يكن مدير
      const adminLink = document.getElementById('admin-link');
      if (adminLink) {
        adminLink.remove();
      }
    }
  }

  updateUIForLoggedOutUser() {
    // إعادة تعيين زر تسجيل الدخول
    const loginBtn = document.getElementById('login-toggle');
    if (loginBtn) {
      loginBtn.innerHTML = `
        <div class="w-8 h-8 flex items-center justify-center text-gray-700 dark:text-gray-300">
          <i class="ri-login-circle-line ri-lg"></i>
        </div>
      `;
      loginBtn.setAttribute('aria-label', 'تسجيل الدخول');
      loginBtn.onclick = () => this.openAuthModal();
    }

    // إزالة رابط لوحة الإدارة
    const adminLink = document.getElementById('admin-link');
    if (adminLink) {
      adminLink.remove();
    }
  }

  // فتح نافذة المصادقة
  openAuthModal() {
    const modal = document.getElementById('auth-modal');
    const modalContent = document.getElementById('auth-modal-content');

    if (modal && modalContent) {
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';

      // تشغيل الانيميشن
      setTimeout(() => {
        modalContent.style.transform = 'scale(1)';
        modalContent.style.opacity = '1';
      }, 10);
    }
  }

  // فتح نافذة الحساب
  openAccountModal() {
    const user = this.getCurrentUser();
    if (!user) {
      this.openAuthModal();
      return;
    }

    const modal = document.getElementById('account-modal');
    const modalContent = document.getElementById('account-modal-content');
    const userInfo = document.getElementById('user-info');

    if (modal && modalContent && userInfo) {
      // تحديث معلومات المستخدم
      userInfo.innerHTML = `
        <div class="flex items-center space-x-4 space-x-reverse mb-6">
          <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold">
            ${user.name.charAt(0).toUpperCase()}
          </div>
          <div>
            <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-100">${user.name}</h4>
            <p class="text-gray-600 dark:text-gray-400">${user.email}</p>
            <p class="text-sm text-gray-500 dark:text-gray-500">معرف المستخدم: ${user.userId}</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div class="flex items-center mb-2">
              <i class="ri-phone-line text-primary ml-2"></i>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">رقم الهاتف</span>
            </div>
            <p class="text-gray-900 dark:text-gray-100">${user.phone}</p>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div class="flex items-center mb-2">
              <i class="ri-map-pin-line text-primary ml-2"></i>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">المحافظة</span>
            </div>
            <p class="text-gray-900 dark:text-gray-100">${user.province}</p>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div class="flex items-center mb-2">
              <i class="ri-shopping-bag-line text-primary ml-2"></i>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الطلبات المكتملة</span>
            </div>
            <p class="text-gray-900 dark:text-gray-100">${user.completedOrders || 0}</p>
          </div>

          <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div class="flex items-center mb-2">
              <i class="ri-vip-crown-line text-primary ml-2"></i>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">الرتبة</span>
            </div>
            <p class="text-gray-900 dark:text-gray-100">${user.rank === 'premium' ? 'مميز' : 'عادي'}</p>
          </div>
        </div>

        <div class="mt-4 bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
          <div class="flex items-center mb-2">
            <i class="ri-calendar-line text-blue-600 ml-2"></i>
            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">تاريخ التسجيل</span>
          </div>
          <p class="text-blue-900 dark:text-blue-100">${new Date(user.createdAt).toLocaleDateString('ar-EG')}</p>
        </div>
      `;

      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';

      // تشغيل الانيميشن
      setTimeout(() => {
        modalContent.style.transform = 'scale(1)';
        modalContent.style.opacity = '1';
      }, 10);
    }
  }

  // إغلاق النوافذ المنبثقة
  closeModal(modalId) {
    const modal = document.getElementById(modalId);
    const modalContent = modal?.querySelector('[id$="-content"]');

    if (modal && modalContent) {
      // تشغيل انيميشن الإغلاق
      modalContent.style.transform = 'scale(0.95)';
      modalContent.style.opacity = '0';

      setTimeout(() => {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
      }, 300);
    }
  }

  // تبديل علامات التبويب في نافذة المصادقة
  switchAuthTab(tab) {
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const modalTitle = document.getElementById('auth-modal-title');

    if (tab === 'login') {
      loginTab.classList.add('bg-white', 'dark:bg-gray-600', 'text-primary', 'shadow-sm');
      loginTab.classList.remove('text-gray-600', 'dark:text-gray-300');
      registerTab.classList.remove('bg-white', 'dark:bg-gray-600', 'text-primary', 'shadow-sm');
      registerTab.classList.add('text-gray-600', 'dark:text-gray-300');

      loginForm.classList.remove('hidden');
      registerForm.classList.add('hidden');
      modalTitle.textContent = 'تسجيل الدخول';
    } else {
      registerTab.classList.add('bg-white', 'dark:bg-gray-600', 'text-primary', 'shadow-sm');
      registerTab.classList.remove('text-gray-600', 'dark:text-gray-300');
      loginTab.classList.remove('bg-white', 'dark:bg-gray-600', 'text-primary', 'shadow-sm');
      loginTab.classList.add('text-gray-600', 'dark:text-gray-300');

      registerForm.classList.remove('hidden');
      loginForm.classList.add('hidden');
      modalTitle.textContent = 'إنشاء حساب جديد';
    }
  }
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>ڤيلا سويتز - VelaSweets</title>
    
    <!-- External CSS Libraries -->
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#f0a030',
                        secondary: '#8B4513'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    },
                    fontFamily: {
                        'andaluzia': ['Andaluzia Personal Use', 'cursive'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Andaluzia Personal Use Font -->
<link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- RemixIcon -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Additional fixes for overflow issues -->
    <style>
        /* Emergency overflow fixes */
        html, body {
            overflow-x: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
            position: relative !important;
        }
        
        /* Hide all scrollbars */
        *::-webkit-scrollbar {
            display: none !important;
        }
        
        * {
            -ms-overflow-style: none !important;
            scrollbar-width: none !important;
        }
        
        /* Ensure no element extends beyond viewport */
        body > * {
            max-width: 100vw !important;
            overflow-x: hidden !important;
        }
        
        /* Fix sidebars positioning */
        .cart-sidebar {
            transition: transform 0.3s ease !important;
        }
        
        .favorites-sidebar {
            transition: transform 0.3s ease !important;
        }
        
        .cart-sidebar:not(.open) {
            transform: translateX(-100%) !important;
        }
        
        .favorites-sidebar:not(.open) {
            transform: translateX(100%) !important;
        }
        
        .cart-sidebar.open {
            transform: translateX(0) !important;
        }
        
        .favorites-sidebar.open {
            transform: translateX(0) !important;
        }
    </style>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="ڤيلا سويتز - متجر متخصص في تقديم أشهى وأفخر أنواع الحلويات العربية بلمسة عصرية وجودة عالية">
    <meta name="keywords" content="حلويات عربية, حلويات فاخرة, باقلاوة, كيك, البصرة, توصيل حلويات">
    <meta name="author" content="VelaSweets">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="ڤيلا سويتز - متجر الحلويات العربية الفاخرة">
    <meta property="og:description" content="استمتع بأشهى الحلويات العربية المصنوعة بمكونات طبيعية 100% وبأيدي أمهر الحلوانيين">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_IQ">
</head>

<body class="bg-white dark:bg-gray-900 min-h-screen transition-colors duration-300 pt-[110px]" data-theme="light">
<!-- Header -->
<header class="bg-white dark:bg-gray-900 shadow-sm fixed top-0 left-0 right-0 z-50 transition-colors duration-300 backdrop-blur-sm bg-white/95 dark:bg-gray-900/95">
<div class="container mx-auto header-container">
            <!-- Logo -->
<div class="logo-section">
                <a href="#" onclick="location.reload(); return false;" class="logo-container flex items-center cursor-pointer hover:opacity-80 transition-opacity">
                    <h1 class="font-bold text-primary font-andaluzia">VelaSweets</h1>
                </a>
</div>
            
            <!-- Header Actions -->
<div class="header-actions">
                <!-- Theme Toggle -->
                <button id="theme-toggle" class="header-btn hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="تغيير المظهر">
                    <i class="ri-sun-line theme-light transition-all"></i>
                    <i class="ri-moon-line theme-dark hidden transition-all"></i>
                </button>
                
                <!-- Language Switcher -->
<div class="relative" id="language-switcher">
                    <button id="language-toggle" class="header-btn hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="تغيير اللغة">
                        <i class="ri-global-line"></i>
</button>
                    <div id="language-dropdown" class="absolute left-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-2 hidden transition-all border dark:border-gray-700" style="z-index: 99999 !important; position: fixed !important;">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" data-lang="ar">
                            العربية
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" data-lang="en">
                            English
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" data-lang="ku">
                            کوردی
                        </a>
</div>
</div>
                
                <!-- Login Button -->
                <button id="login-toggle" class="header-btn hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="تسجيل الدخول">
                    <i class="ri-login-circle-line"></i>
</button>

                <!-- Favorites Button -->
                <button id="favorites-toggle" class="header-btn hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="المفضلة">
                    <i class="ri-heart-line"></i>
                    <span id="favorites-count" class="header-badge bg-red-500 text-white hidden">0</span>
</button>

                <!-- Cart Button -->
                <button id="cart-toggle" class="header-btn hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="سلة التسوق">
                    <i class="ri-shopping-cart-line"></i>
                    <span id="cart-count" class="header-badge bg-primary text-white">0</span>
</button>
</div>
</div>
        
        <!-- Navigation Menu -->
<nav class="bg-white dark:bg-gray-900 border-t border-gray-100 dark:border-gray-700 transition-colors duration-300">
<div class="container mx-auto nav-container">
<ul class="nav-menu">
                    <li><a href="#" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;" class="nav-item active">الرئيسية</a></li>
                    <li><a href="#products" class="nav-item text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">المنتجات</a></li>
                    <li><a href="#about" class="nav-item text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">من نحن</a></li>
                    <li><a href="#contact" class="nav-item text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">تواصل معنا</a></li>
</ul>
</div>
</nav>
</header>

<!-- Hero Section -->
<section class="hero-section py-20 md:py-32 bg-white dark:bg-gray-900 transition-colors duration-300">
<div class="container mx-auto px-4 w-full">
<div class="max-w-xl">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-gray-100 mb-4 fade-in">حلويات عربية فاخرة</h2>
                <p class="text-lg text-blue-600 dark:text-blue-300 mb-8 fade-in font-medium">استمتع بأشهى الحلويات العربية المصنوعة بمكونات طبيعية 100% وبأيدي أمهر الحلوانيين</p>
                <div class="flex flex-col sm:flex-row gap-4 fade-in">
                    <a href="#products" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all hover:scale-105 whitespace-nowrap text-center">
                        <i class="ri-shopping-bag-line ml-2"></i>
                        تسوق الآن
                    </a>
                    <a href="#about" class="border border-primary text-primary px-6 py-3 rounded-button font-medium hover:bg-primary hover:bg-opacity-10 transition-all hover:scale-105 whitespace-nowrap text-center">
                        <i class="ri-information-line ml-2"></i>
                        اعرف المزيد
                    </a>
</div>
</div>
</div>
</section>

<!-- Products Section -->
<section id="products" class="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
<div class="container mx-auto px-4">
<div class="text-center mb-12">
<h2 class="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">منتجاتنا المميزة</h2>
<p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">نقدم لكم تشكيلة واسعة من الحلويات العربية الفاخرة المصنوعة بعناية فائقة ومكونات طبيعية</p>
</div>
            
            <!-- Products Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Products will be dynamically loaded here by products.js -->
                
                
</div>
</div>
</section>

<!-- About Section -->
<section id="about" class="py-16 bg-white dark:bg-gray-900 transition-colors duration-300">
<div class="container mx-auto px-4">
<div class="text-center mb-12">
<h2 class="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">من نحن</h2>
<p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">ڤيلا سويتز هو متجر متخصص في تقديم أشهى وأفخر أنواع الحلويات العربية بلمسة عصرية وجودة عالية</p>
</div>

<div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md about-card transition-colors duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-award-line ri-2x"></i>
</div>
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2">جودة عالية</h3>
<p class="text-gray-600 dark:text-gray-300">نستخدم أفضل المكونات الطبيعية 100% لضمان جودة منتجاتنا</p>
</div>
                
                <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md about-card transition-colors duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-truck-line ri-2x"></i>
</div>
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2">توصيل سريع</h3>
<p class="text-gray-600 dark:text-gray-300">نوفر خدمة توصيل سريعة لجميع مناطق العراق خلال 24 ساعة</p>
</div>

                <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md about-card transition-colors duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-customer-service-line ri-2x"></i>
</div>
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2">خدمة ممتازة</h3>
<p class="text-gray-600 dark:text-gray-300">فريق خدمة عملاء متميز جاهز للرد على استفساراتكم على مدار الساعة</p>
</div>
</div>
</div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
<div class="container mx-auto px-4">
<div class="text-center mb-12">
<h2 class="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">تواصل معنا</h2>
<p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">نحن سعداء بتواصلكم معنا في أي وقت للاستفسار أو تقديم الطلبات الخاصة</p>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Information -->
<div>
<div class="bg-white dark:bg-gray-700 p-8 rounded-lg shadow-md transition-colors duration-300">
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-6">معلومات الاتصال</h3>
<div class="space-y-4">
<div class="flex items-start">
                                <div class="w-10 h-10 flex-shrink-0 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-phone-line ri-lg"></i>
</div>
<div class="mr-4">
<h4 class="text-gray-800 dark:text-white font-semibold">الهاتف</h4>
<p class="text-green-600 dark:text-green-300 font-semibold">07726455910</p>
</div>
</div>

<div class="flex items-start">
                                <div class="w-10 h-10 flex-shrink-0 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-mail-line ri-lg"></i>
</div>
<div class="mr-4">
<h4 class="text-gray-800 dark:text-white font-semibold">البريد الإلكتروني</h4>
<p class="text-purple-600 dark:text-purple-300 font-semibold"><EMAIL></p>
</div>
</div>

<div class="flex items-start">
                                <div class="w-10 h-10 flex-shrink-0 text-primary flex items-center justify-center bg-primary bg-opacity-10 rounded-full">
<i class="ri-map-pin-line ri-lg"></i>
</div>
<div class="mr-4">
<h4 class="text-gray-800 dark:text-white font-semibold">العنوان</h4>
<p class="text-blue-600 dark:text-blue-300 font-semibold">البصرة - العراق</p>
</div>
</div>
</div>
                        
<div class="mt-8">
<h4 class="text-gray-800 dark:text-white font-semibold mb-4">تابعنا على</h4>
<div class="flex space-x-4 space-x-reverse">
                                <a href="#" class="w-10 h-10 rounded-full bg-[#1877F2] flex items-center justify-center text-white hover:bg-[#166FE5] transition-all transform hover:scale-110" aria-label="فيسبوك">
<i class="ri-facebook-fill"></i>
</a>
                                <a href="#" class="w-10 h-10 rounded-full bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#F77737] flex items-center justify-center text-white hover:opacity-90 transition-all transform hover:scale-110" aria-label="إنستجرام">
<i class="ri-instagram-line"></i>
</a>
                                <a href="#" class="w-10 h-10 rounded-full bg-[#000000] flex items-center justify-center text-white hover:bg-[#333333] transition-all transform hover:scale-110" aria-label="تيك توك">
<i class="ri-tiktok-fill"></i>
</a>
</div>
</div>
</div>
</div>

                <!-- Contact Form -->
<div>
<div class="bg-white dark:bg-gray-700 p-8 rounded-lg shadow-md transition-colors duration-300">
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-6">أرسل رسالة</h3>
<form>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
<div>
<label for="name" class="block text-gray-800 dark:text-gray-200 font-semibold mb-2">الاسم</label>
                                    <input type="text" id="name" name="name" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل اسمك" required>
</div>
<div>
<label for="email" class="block text-gray-800 dark:text-gray-200 font-semibold mb-2">البريد الإلكتروني</label>
                                    <input type="email" id="email" name="email" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل بريدك الإلكتروني" required>
</div>
</div>

<div class="mb-4">
<label for="subject" class="block text-gray-800 dark:text-gray-200 font-semibold mb-2">الموضوع</label>
                                <input type="text" id="subject" name="subject" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل موضوع الرسالة" required>
</div>

<div class="mb-6">
<label for="message" class="block text-gray-800 dark:text-gray-200 font-semibold mb-2">الرسالة</label>
                                <textarea id="message" name="message" rows="4" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل رسالتك" required></textarea>
</div>
                            
                            <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all hover:scale-105 whitespace-nowrap">
                                <i class="ri-send-plane-line ml-2"></i>
                                إرسال الرسالة
                            </button>
</form>
</div>
</div>
</div>
</div>
</section>

<!-- Cart Sidebar -->
<div id="cart-sidebar" class="cart-sidebar fixed top-0 left-0 w-full md:w-[420px] h-full bg-white dark:bg-gray-900 shadow-2xl z-50 overflow-hidden flex flex-col transition-colors duration-300">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
<div class="flex justify-between items-center">
<h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">سلة التسوق</h3>
<button id="close-cart" class="w-10 h-10 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
        </div>

<div id="cart-items" class="flex-1 overflow-y-auto px-6 py-4 space-y-4">
<!-- Cart items will be added here dynamically -->
<div class="text-center text-gray-500 dark:text-gray-400 py-8">
                <i class="ri-shopping-cart-line ri-3x mb-4 opacity-50"></i>
                <p>السلة فارغة</p>
</div>
</div>

        <div class="p-6 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300">
<div class="flex justify-between items-center mb-4">
<span class="text-gray-700 dark:text-gray-300 font-medium">المجموع</span>
                <span id="cart-total" class="text-primary font-bold text-lg">0 د.ع</span>
</div>
            <button id="checkout-btn" class="w-full bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap">
                <i class="ri-bank-card-line ml-2"></i>
                إتمام الطلب
            </button>
</div>
</div>

<!-- Favorites Sidebar -->
<div id="favorites-sidebar" class="favorites-sidebar fixed top-0 right-0 w-full md:w-[420px] h-full bg-white dark:bg-gray-900 shadow-2xl z-50 overflow-hidden flex flex-col transition-colors duration-300">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
<div class="flex justify-between items-center">
<h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                    <i class="ri-heart-fill text-red-500 ml-2"></i>
                    المفضلة
                </h3>
<button id="close-favorites" class="w-10 h-10 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
<i class="ri-close-line ri-lg"></i>
</button>
</div>
        </div>

<div id="favorites-items" class="flex-1 overflow-y-auto px-6 py-4 space-y-4">
<!-- Favorites items will be added here dynamically -->
<div class="text-center text-gray-500 dark:text-gray-400 py-8">
                <i class="ri-heart-line ri-3x mb-4 opacity-50"></i>
                <p>لا توجد منتجات في المفضلة</p>
</div>
</div>

        <div class="p-6 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300">
            <button id="clear-favorites-btn" class="w-full bg-red-500 text-white px-6 py-3 rounded-button font-medium hover:bg-red-600 transition-all hover:scale-105 whitespace-nowrap">
                <i class="ri-delete-bin-line ml-2"></i>
                مسح جميع المفضلة
            </button>
</div>
</div>

<!-- Checkout Modal -->
    <div id="checkout-modal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-50 flex items-center justify-center hidden p-4">
<div class="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md mx-4 overflow-hidden transition-colors duration-300">
<div class="p-6">
<div class="flex justify-between items-center mb-6">
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">إتمام الطلب</h3>
                    <button id="close-checkout" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 z-10 relative" aria-label="إغلاق">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-close-line ri-lg"></i>
</div>
</button>
</div>

<form id="checkout-form">
<div class="mb-4">
<label for="customer-name" class="block text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل</label>
                        <input type="text" id="customer-name" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل اسمك الكامل" required>
</div>

<div class="mb-4">
<label for="customer-phone" class="block text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                        <input type="tel" id="customer-phone" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل رقم هاتفك" required>
</div>

<div class="mb-4">
<label for="customer-province" class="block text-gray-700 dark:text-gray-300 mb-2">المحافظة</label>
                        <select id="customer-province" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors pr-8" required>
<option value="" disabled selected>اختر المحافظة</option>
<option value="بغداد">بغداد</option>
<option value="البصرة">البصرة</option>
<option value="نينوى">نينوى</option>
<option value="أربيل">أربيل</option>
<option value="النجف">النجف</option>
<option value="كربلاء">كربلاء</option>
<option value="السليمانية">السليمانية</option>
<option value="ذي قار">ذي قار</option>
<option value="الأنبار">الأنبار</option>
<option value="ديالى">ديالى</option>
<option value="كركوك">كركوك</option>
<option value="صلاح الدين">صلاح الدين</option>
<option value="بابل">بابل</option>
<option value="ميسان">ميسان</option>
<option value="واسط">واسط</option>
<option value="المثنى">المثنى</option>
<option value="دهوك">دهوك</option>
<option value="القادسية">القادسية</option>
</select>
</div>

<div class="mb-6">
<label for="customer-address" class="block text-gray-700 dark:text-gray-300 mb-2">العنوان التفصيلي</label>
                        <textarea id="customer-address" rows="3" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded focus:outline-none focus:border-primary transition-colors" placeholder="أدخل العنوان التفصيلي" required></textarea>
</div>

<div class="mb-6">
<label class="block text-gray-700 dark:text-gray-300 mb-2">طريقة الدفع</label>
<div class="p-4 rounded-lg border-2 border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20">
                            <div class="flex items-center justify-center">
                                <i class="ri-hand-coin-line text-green-600 dark:text-green-400 text-2xl ml-3"></i>
                                <div class="text-center">
                                    <span class="font-bold text-green-800 dark:text-green-200 text-lg">الدفع عند الاستلام</span>
                                    <p class="text-green-600 dark:text-green-300 text-sm mt-1">ادفع نقداً عند وصول طلبك</p>
                                </div>
                                    </div>
                            <input type="hidden" name="payment-method" value="cash">
                                </div>
</div>
                    
                    <!-- Payment Info Banner -->
                    <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                        <div class="flex items-center justify-center text-blue-800 dark:text-blue-200">
                            <i class="ri-information-line text-lg ml-2"></i>
                            <span class="text-sm font-medium">جميع الطلبات تُدفع نقداً عند الاستلام - لا توجد رسوم إضافية</span>
</div>
</div>
                    
                    <button type="submit" class="w-full bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all hover:scale-105 whitespace-nowrap">
                        <i class="ri-check-line ml-2"></i>
                        تأكيد الطلب (دفع عند الاستلام)
                    </button>
</form>
</div>
</div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-50 flex items-center justify-center hidden">
<div class="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md mx-4 p-6 text-center transition-colors duration-300">
            <div class="w-20 h-20 mx-auto mb-6 text-green-500 flex items-center justify-center bg-green-100 dark:bg-green-900 rounded-full">
<i class="ri-check-line ri-3x"></i>
</div>
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2">تم تقديم طلبك بنجاح!</h3>
<p class="text-gray-600 dark:text-gray-300 mb-4">سيتم التواصل معك قريباً لتأكيد الطلب وترتيب موعد التوصيل.</p>
<div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-3 mb-6">
                <div class="flex items-center justify-center text-green-800 dark:text-green-200">
                    <i class="ri-hand-coin-line text-xl ml-2"></i>
                    <span class="font-medium">الدفع نقداً عند الاستلام</span>
                </div>
</div>
            <button id="close-success" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all hover:scale-105 whitespace-nowrap">
                <i class="ri-home-line ml-2"></i>
                العودة للمتجر
            </button>
</div>
</div>

<!-- Mobile Menu -->
<div id="mobile-menu" class="fixed inset-y-0 right-0 w-64 bg-white dark:bg-gray-900 shadow-lg transform translate-x-full transition-all duration-300 ease-in-out z-50">
<div class="p-6">
<div class="flex justify-between items-center mb-6">
<h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">القائمة</h3>
                <button id="close-menu" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-close-line ri-lg"></i>
</div>
</button>
</div>
<nav>
<ul class="space-y-4">
                    <li><a href="#" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;" class="block py-2 text-primary font-medium transition-colors">الرئيسية</a></li>
                    <li><a href="#products" class="block py-2 text-gray-600 dark:text-gray-300 hover:text-primary transition-colors">المنتجات</a></li>
                    <li><a href="#about" class="block py-2 text-gray-600 dark:text-gray-300 hover:text-primary transition-colors">من نحن</a></li>
                    <li><a href="#contact" class="block py-2 text-gray-600 dark:text-gray-300 hover:text-primary transition-colors">تواصل معنا</a></li>
</ul>
</nav>
</div>
</div>

    <!-- Custom Confirmation Modal -->
    <div id="custom-modal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-60 hidden flex items-center justify-center p-4 transition-all duration-300">
        <div id="modal-content" class="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-md w-full mx-4 transform scale-95 opacity-0 transition-all duration-300">
            <!-- Modal Header -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div id="modal-icon" class="w-12 h-12 rounded-full flex items-center justify-center ml-4">
                        <!-- Icon will be inserted here -->
                    </div>
                    <h3 id="modal-title" class="text-xl font-bold text-gray-800 dark:text-gray-100">
                        <!-- Title will be inserted here -->
                    </h3>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <p id="modal-message" class="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                    <!-- Message will be inserted here -->
                </p>
            </div>

            <!-- Modal Footer -->
            <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex gap-3 justify-end">
                <button id="modal-cancel" class="px-6 py-3 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-button font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105">
                    إلغاء
                </button>
                <button id="modal-confirm" class="px-6 py-3 bg-red-500 text-white rounded-button font-medium hover:bg-red-600 transition-all duration-200 hover:scale-105">
                    <!-- Confirm text will be inserted here -->
                </button>
            </div>
        </div>
    </div>

    <!-- Authentication Modal -->
    <div id="auth-modal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-60 hidden flex items-center justify-center p-4 transition-all duration-300">
        <div id="auth-modal-content" class="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-md w-full mx-4 transform scale-95 opacity-0 transition-all duration-300">
            <!-- Modal Header -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 id="auth-modal-title" class="text-xl font-bold text-gray-800 dark:text-gray-100">تسجيل الدخول</h3>
                    <button id="close-auth-modal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="إغلاق">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-close-line ri-lg"></i>
                        </div>
                    </button>
                </div>

                <!-- Auth Tabs -->
                <div class="flex mt-4 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    <button id="login-tab" class="auth-tab flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 bg-white dark:bg-gray-600 text-primary shadow-sm">
                        تسجيل الدخول
                    </button>
                    <button id="register-tab" class="auth-tab flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100">
                        إنشاء حساب
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <!-- Login Form -->
                <form id="login-form" class="auth-form space-y-4">
                    <div class="form-group">
                        <label for="login-identifier" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            البريد الإلكتروني أو رقم الهاتف
                        </label>
                        <input type="text" id="login-identifier" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                               placeholder="أدخل البريد الإلكتروني أو رقم الهاتف">
                    </div>

                    <div class="form-group">
                        <label for="login-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" id="login-password" required
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                                   placeholder="أدخل كلمة المرور">
                            <button type="button" class="password-toggle absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                    onclick="togglePassword('login-password')">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all duration-200 hover:scale-105 flex items-center justify-center">
                        <i class="ri-login-circle-line ml-2"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <!-- Register Form -->
                <form id="register-form" class="auth-form space-y-4 hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="register-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                الاسم الكامل *
                            </label>
                            <input type="text" id="register-name" required
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                                   placeholder="أدخل اسمك الكامل">
                        </div>
                        <div class="form-group">
                            <label for="register-email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                البريد الإلكتروني *
                            </label>
                            <input type="email" id="register-email" required
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="register-phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                رقم الهاتف *
                            </label>
                            <input type="tel" id="register-phone" required
                                   pattern="^07[0-9]{9}$" maxlength="11"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                                   placeholder="07xxxxxxxxx">
                        </div>
                        <div class="form-group">
                            <label for="register-province" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                المحافظة *
                            </label>
                            <select id="register-province" required
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200">
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الموصل">الموصل</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="بابل">بابل</option>
                                <option value="ديالى">ديالى</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="واسط">واسط</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                                <option value="كركوك">كركوك</option>
                                <option value="دهوك">دهوك</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="ميسان">ميسان</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="register-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            كلمة المرور *
                        </label>
                        <div class="relative">
                            <input type="password" id="register-password" required minlength="6"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                                   placeholder="أدخل كلمة مرور قوية">
                            <button type="button" class="password-toggle absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                                    onclick="togglePassword('register-password')">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                        <div id="password-strength" class="mt-2 text-xs"></div>
                    </div>

                    <div class="form-group">
                        <label for="register-confirm-password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            تأكيد كلمة المرور *
                        </label>
                        <input type="password" id="register-confirm-password" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary focus:ring-opacity-20 transition-all duration-200"
                               placeholder="أعد إدخال كلمة المرور">
                    </div>

                    <div class="form-group">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" id="agree-terms" required
                                   class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">
                                أوافق على
                                <a href="terms-conditions.html" target="_blank" class="text-primary hover:underline">الشروط والأحكام</a>
                                و
                                <a href="privacy-policy.html" target="_blank" class="text-primary hover:underline">سياسة الخصوصية</a>
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="w-full bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-all duration-200 hover:scale-105 flex items-center justify-center">
                        <i class="ri-user-add-line ml-2"></i>
                        إنشاء حساب
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Account Modal -->
    <div id="account-modal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-60 hidden flex items-center justify-center p-4 transition-all duration-300">
        <div id="account-modal-content" class="bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-w-lg w-full mx-4 transform scale-95 opacity-0 transition-all duration-300">
            <!-- Modal Header -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100">الملف الشخصي</h3>
                    <button id="close-account-modal" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="إغلاق">
                        <div class="w-8 h-8 flex items-center justify-center">
                            <i class="ri-close-line ri-lg"></i>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div id="user-info" class="space-y-4">
                    <!-- User info will be populated here -->
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button id="logout-btn" class="w-full bg-red-500 text-white px-6 py-3 rounded-button font-medium hover:bg-red-600 transition-all duration-200 hover:scale-105 flex items-center justify-center">
                        <i class="ri-logout-circle-line ml-2"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 z-40 hidden transition-colors duration-300"></div>

<!-- Footer -->
<footer class="bg-gray-800 dark:bg-gray-900 text-white py-12 transition-colors duration-300">
<div class="container mx-auto px-4">
<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Company Info -->
<div>
<h3 class="text-xl font-bold mb-4">ڤيلا سويتز</h3>
<p class="text-gray-400 dark:text-gray-300 mb-4">متجر متخصص في تقديم أشهى وأفخر أنواع الحلويات العربية بلمسة عصرية وجودة عالية</p>
<div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="w-10 h-10 rounded-full bg-[#1877F2] flex items-center justify-center text-white hover:bg-[#166FE5] transition-all transform hover:scale-110" aria-label="فيسبوك">
<i class="ri-facebook-fill"></i>
</a>
                        <a href="#" class="w-10 h-10 rounded-full bg-gradient-to-r from-[#833AB4] via-[#FD1D1D] to-[#F77737] flex items-center justify-center text-white hover:opacity-90 transition-all transform hover:scale-110" aria-label="إنستجرام">
<i class="ri-instagram-line"></i>
</a>
                        <a href="#" class="w-10 h-10 rounded-full bg-[#000000] flex items-center justify-center text-white hover:bg-[#333333] transition-all transform hover:scale-110" aria-label="تيك توك">
<i class="ri-tiktok-fill"></i>
</a>
</div>
</div>

                <!-- Quick Links -->
<div>
<h3 class="text-xl font-bold mb-4">روابط سريعة</h3>
<ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-home-line ml-2 group-hover:text-primary transition-colors"></i>
                            الرئيسية
                        </a></li>
                        <li><a href="#products" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-cake-line ml-2 group-hover:text-primary transition-colors"></i>
                            المنتجات
                        </a></li>
                        <li><a href="#about" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-information-line ml-2 group-hover:text-primary transition-colors"></i>
                            من نحن
                        </a></li>
                        <li><a href="#contact" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-customer-service-line ml-2 group-hover:text-primary transition-colors"></i>
                            تواصل معنا
                        </a></li>
                        <li><a href="privacy-policy.html" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-shield-check-line ml-2 group-hover:text-primary transition-colors"></i>
                            سياسة الخصوصية
                        </a></li>
                        <li><a href="terms-conditions.html" class="text-gray-400 dark:text-gray-300 hover:text-white transition-colors flex items-center group">
                            <i class="ri-file-text-line ml-2 group-hover:text-primary transition-colors"></i>
                            الشروط والأحكام
                        </a></li>
</ul>
</div>
                
                <!-- Contact Info -->
<div>
<h3 class="text-xl font-bold mb-4">اتصل بنا</h3>
<ul class="space-y-4">
<li class="flex">
<div class="w-6 h-6 flex-shrink-0 text-primary flex items-center justify-center">
<i class="ri-phone-line"></i>
</div>
<span class="mr-3 text-green-600 dark:text-green-300 font-semibold">07726455910</span>
</li>
<li class="flex">
<div class="w-6 h-6 flex-shrink-0 text-primary flex items-center justify-center">
<i class="ri-mail-line"></i>
</div>
<span class="mr-3 text-purple-600 dark:text-purple-300 font-semibold"><EMAIL></span>
</li>
<li class="flex">
<div class="w-6 h-6 flex-shrink-0 text-primary flex items-center justify-center">
<i class="ri-map-pin-line"></i>
</div>
<span class="mr-3 text-blue-600 dark:text-blue-300 font-semibold">البصرة - العراق</span>
</li>
</ul>
</div>
</div>

<div class="border-t border-gray-700 dark:border-gray-600 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-400 dark:text-gray-300 mb-4 md:mb-0">جميع الحقوق محفوظة © 2025 VelaSweets</p>
<div class="flex items-center space-x-4 space-x-reverse">
                    <div class="text-gray-300 dark:text-gray-400 text-sm font-medium">طريقة الدفع:</div>

                    <!-- Cash on Delivery Only -->
                    <div class="bg-green-600 rounded-lg px-4 py-2 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <i class="ri-hand-coin-line text-white text-lg"></i>
                            <span class="text-sm font-bold text-white">الدفع عند الاستلام</span>
                        </div>
                    </div>
</div>
</div>
</div>
</footer>



    <!-- JavaScript Files -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/modal.js"></script>
    <script src="assets/js/auth-system.js"></script>
    <script src="assets/js/products.js"></script>
    <script src="assets/js/cart.js"></script>
    <script src="assets/js/favorites.js"></script>
    <script src="assets/js/checkout.js"></script>
    
    <!-- Fix for any potential scrollbar/overflow issues -->
    <script>
        // Ensure no horizontal scrolling
        document.addEventListener('DOMContentLoaded', function() {
            // Force remove any horizontal overflow
            document.body.style.overflowX = 'hidden';
            document.documentElement.style.overflowX = 'hidden';
            
            // Check and fix any elements causing overflow
            const checkOverflow = () => {
                const docWidth = document.documentElement.offsetWidth;
                const elements = document.querySelectorAll('*');
                
                elements.forEach(el => {
                    if (el.offsetWidth > docWidth) {
                        console.warn('Element causing overflow:', el);
                        el.style.maxWidth = '100%';
                        el.style.overflowX = 'hidden';
                    }
                });
            };
            
            // Run check on load and after a delay
            checkOverflow();
            setTimeout(checkOverflow, 500);
            
            // Ensure sidebars are properly positioned
            const cartSidebar = document.getElementById('cart-sidebar');
            const favoritesSidebar = document.getElementById('favorites-sidebar');
            
            if (cartSidebar && !cartSidebar.classList.contains('open')) {
                cartSidebar.style.transform = 'translateX(-100%)';
            }
            
            if (favoritesSidebar && !favoritesSidebar.classList.contains('open')) {
                favoritesSidebar.style.transform = 'translateX(100%)';
            }

        });
    </script>

    <script>
        // تهيئة نظام المصادقة
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // إنشاء مدير المصادقة
                window.authManager = new AuthManager();
                console.log('✅ تم تهيئة نظام المصادقة بنجاح');

                // إعداد أحداث النقر
                setupAuthEventListeners();

                // إعداد نماذج المصادقة
                setupAuthForms();
            } catch (error) {
                console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
            }
        });

        function setupAuthEventListeners() {
            try {
                // زر تسجيل الدخول في الهيدر
                const loginToggle = document.getElementById('login-toggle');
                if (loginToggle) {
                    loginToggle.addEventListener('click', () => {
                        if (window.authManager && window.authManager.isAuthenticated()) {
                            window.authManager.openAccountModal();
                        } else if (window.authManager) {
                            window.authManager.openAuthModal();
                        }
                    });
                } else {
                    console.warn('⚠️ لم يتم العثور على زر تسجيل الدخول');
                }

            // أزرار إغلاق النوافذ المنبثقة
            const closeAuthModal = document.getElementById('close-auth-modal');
            const closeAccountModal = document.getElementById('close-account-modal');

            if (closeAuthModal) {
                closeAuthModal.addEventListener('click', () => {
                    window.authManager.closeModal('auth-modal');
                });
            }

            if (closeAccountModal) {
                closeAccountModal.addEventListener('click', () => {
                    window.authManager.closeModal('account-modal');
                });
            }

            // علامات التبويب
            const loginTab = document.getElementById('login-tab');
            const registerTab = document.getElementById('register-tab');

            if (loginTab) {
                loginTab.addEventListener('click', () => {
                    window.authManager.switchAuthTab('login');
                });
            }

            if (registerTab) {
                registerTab.addEventListener('click', () => {
                    window.authManager.switchAuthTab('register');
                });
            }

            // زر تسجيل الخروج
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => {
                    window.authManager.logout();
                    window.authManager.closeModal('account-modal');

                    // إظهار رسالة نجاح
                    if (window.customModal) {
                        window.customModal.success('تم تسجيل الخروج بنجاح');
                    }
                });
            }

            // إغلاق النوافذ عند النقر خارجها
            document.getElementById('auth-modal')?.addEventListener('click', (e) => {
                if (e.target.id === 'auth-modal') {
                    window.authManager.closeModal('auth-modal');
                }
            });

            document.getElementById('account-modal')?.addEventListener('click', (e) => {
                if (e.target.id === 'account-modal') {
                    window.authManager.closeModal('account-modal');
                }
            });
            } catch (error) {
                console.error('❌ خطأ في إعداد أحداث المصادقة:', error);
            }
        }

        function setupAuthForms() {
            // نموذج تسجيل الدخول
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const identifier = document.getElementById('login-identifier').value.trim();
                    const password = document.getElementById('login-password').value;

                    try {
                        await window.authManager.login(identifier, password);

                        // إغلاق النافذة المنبثقة
                        window.authManager.closeModal('auth-modal');

                        // إظهار رسالة نجاح
                        if (window.customModal) {
                            window.customModal.success('تم تسجيل الدخول بنجاح! مرحباً بك في ڤيلا سويتز');
                        }

                        // مسح النموذج
                        loginForm.reset();

                    } catch (error) {
                        if (window.customModal) {
                            window.customModal.error(error.message);
                        }
                    }
                });
            }

            // نموذج التسجيل
            const registerForm = document.getElementById('register-form');
            if (registerForm) {
                registerForm.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const password = document.getElementById('register-password').value;
                    const confirmPassword = document.getElementById('register-confirm-password').value;

                    // التحقق من تطابق كلمات المرور
                    if (password !== confirmPassword) {
                        if (window.customModal) {
                            window.customModal.error('كلمات المرور غير متطابقة');
                        }
                        return;
                    }

                    const userData = {
                        name: document.getElementById('register-name').value.trim(),
                        email: document.getElementById('register-email').value.trim(),
                        phone: document.getElementById('register-phone').value.trim(),
                        province: document.getElementById('register-province').value,
                        password: password
                    };

                    try {
                        await window.authManager.register(userData);

                        // إغلاق النافذة المنبثقة
                        window.authManager.closeModal('auth-modal');

                        // إظهار رسالة نجاح
                        if (window.customModal) {
                            window.customModal.success(`مرحباً ${userData.name}! تم إنشاء حسابك بنجاح في ڤيلا سويتز`);
                        }

                        // مسح النموذج
                        registerForm.reset();

                    } catch (error) {
                        if (window.customModal) {
                            window.customModal.error(error.message);
                        }
                    }
                });
            }

            // مراقبة قوة كلمة المرور
            const registerPassword = document.getElementById('register-password');
            if (registerPassword) {
                registerPassword.addEventListener('input', (e) => {
                    const password = e.target.value;
                    const strengthDiv = document.getElementById('password-strength');

                    if (password && strengthDiv && window.authManager) {
                        const validation = window.authManager.validatePasswordStrength(password);

                        let strengthText = '';
                        let strengthClass = '';

                        if (validation.score >= 4) {
                            strengthText = 'قوية جداً';
                            strengthClass = 'text-green-600';
                        } else if (validation.score >= 3) {
                            strengthText = 'قوية';
                            strengthClass = 'text-green-500';
                        } else if (validation.score >= 2) {
                            strengthText = 'متوسطة';
                            strengthClass = 'text-yellow-500';
                        } else {
                            strengthText = 'ضعيفة';
                            strengthClass = 'text-red-500';
                        }

                        strengthDiv.innerHTML = `
                            <div class="flex items-center justify-between">
                                <span class="${strengthClass} font-medium">قوة كلمة المرور: ${strengthText}</span>
                                <div class="flex space-x-1 space-x-reverse">
                                    ${Array.from({length: 5}, (_, i) =>
                                        `<div class="w-2 h-2 rounded-full ${i < validation.score ? 'bg-current' : 'bg-gray-300'} ${strengthClass}"></div>`
                                    ).join('')}
                                </div>
                            </div>
                            ${validation.feedback.length > 0 ?
                                `<div class="text-gray-500 text-xs mt-1">يجب أن تحتوي على: ${validation.feedback.join(', ')}</div>`
                                : ''
                            }
                        `;
                    }
                });
            }
        }

        // دالة تبديل إظهار كلمة المرور
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ri-eye-off-line';
            } else {
                input.type = 'password';
                icon.className = 'ri-eye-line';
            }
        }
    </script>

    <!-- Initialize Cart and Favorites -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // تهيئة مدير السلة
                window.cartManager = new CartManager();
                console.log('✅ تم تهيئة مدير السلة بنجاح');

                // تهيئة مدير المفضلة
                window.favoritesManager = new FavoritesManager();
                console.log('✅ تم تهيئة مدير المفضلة بنجاح');

                // تهيئة مراقب المفضلة
                if (typeof initFavoritesObserver === 'function') {
                    initFavoritesObserver();
                    console.log('✅ تم تهيئة مراقب المفضلة بنجاح');
                }

                // تهيئة تأثير الهيدر عند التمرير
                initHeaderScrollEffect();

            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
            }
        });

        // دالة تأثير الهيدر عند التمرير
        function initHeaderScrollEffect() {
            const header = document.querySelector('header');
            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;

                // تحديد الظل حسب الثيم
                const isDark = document.body.dataset.theme === 'dark';
                
                if (currentScrollY > 100) {
                    // إضافة ظل أكثر عند التمرير
                    if (isDark) {
                        header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
                    } else {
                        header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
                    }
                    header.style.backdropFilter = 'blur(15px)';
                } else {
                    // ظل أقل في الأعلى
                    if (isDark) {
                        header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                    } else {
                        header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
                    }
                    header.style.backdropFilter = 'blur(10px)';
                }

                lastScrollY = currentScrollY;
            });
        }
    </script>
</body>
</html>

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# Temporary files
tmp/
temp/

# Local development server files
.cache/

# Package manager lock files
package-lock.json
yarn.lock

# Backup files
*.bak
*.backup

# Development tools
.eslintcache

# Testing
__tests__/
test/
*.test.js
*.spec.js

# Documentation build
docs/build/

# Local configuration files
config.local.js
config.development.js

# Database files (if any)
*.db
*.sqlite
*.sqlite3

# Assets that shouldn't be tracked
assets/images/temp/
assets/uploads/

# Cache directories
.sass-cache/
.npm/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# IDE and editor files
*.sublime-project
*.sublime-workspace
.vscode/
.idea/
*.iml

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
# Add any project-specific files or directories that shouldn't be tracked 